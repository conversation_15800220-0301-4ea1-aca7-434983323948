"use client";

import React, { useEffect, useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Trash2 } from "lucide-react";
import { PayslipNumberInput } from "@/components/payroll/payslip-controls/payslip-number-input";
import {
  RepeatingControl,
  ZeroizeControl,
  SectionHeader,
} from "@/components/payroll/payslip-controls";
import { RateSelectorControl } from "@/components/payroll/payslip-controls/rate-selector-control";

import {
  usePayslip,
  usePayslipItemTypes,
  useUpsertPayslipLineItemMutation,
  useDeletePayslipLineItemMutation,
  useCreatePayslipMutation,
} from "@/hooks/tanstack-query/usePayslip";
import { useQueryClient } from "@tanstack/react-query";
import { useEmployerDBContext } from "@/providers/employer-db-provider";
import {
  useEmployeesQuery,
  useUpdateEmployeeMutation,
} from "@/hooks/tanstack-query/useEmployeesQuery";
import { usePayslipReadOnly } from "@/providers/payslip-readonly-provider";
import { BasicPaySectionSkeleton } from "./skeleton-loaders";

// Define interfaces for each pay element type
interface PayElement {
  id: string;
  isRepeating: boolean;
  zeroizeNext: boolean;
}

interface SalaryElement extends PayElement {
  type: "salary";
  amount: number;
  periodType: string;
}

interface DailyElement extends PayElement {
  type: "daily";
  rate: number;
  days: number;
  // Calculated amount (days * rate)
  amount?: number;
  // Track the source of the rate (selector or manual input)
  rateSource?: "selector" | "manual";
  // Store the rate label when selected from the selector
  rateLabel?: string;
}

interface HourlyElement extends PayElement {
  type: "hourly";
  rate: number;
  hours: number;
  // Calculated amount (hours * rate)
  amount?: number;
  // Track the source of the rate (selector or manual input)
  rateSource?: "selector" | "manual";
  // Store the rate label when selected from the selector
  rateLabel?: string;
}

export type BasicPayElement = SalaryElement | DailyElement | HourlyElement;

export interface BasicSectionData {
  elements: BasicPayElement[];
}

interface PayslipBasicSectionProps {
  employeeId: string;
  periodId: string;
  periodType: string;
}

export const PayslipBasicSection: React.FC<PayslipBasicSectionProps> = ({
  employeeId,
  periodId,
  periodType,
}) => {
  // Get read-only state from context
  const { isReadOnly, getDisabledClassName } = usePayslipReadOnly();

  const initRef = useRef(false);
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  const { data, isLoading } = usePayslip(employeeId, periodId);
  const { data: itemTypes } = usePayslipItemTypes();
  const upsert = useUpsertPayslipLineItemMutation(employeeId, periodId);
  const del = useDeletePayslipLineItemMutation(employeeId, periodId);
  const createPayslip = useCreatePayslipMutation(employeeId, periodId);
  const queryClient = useQueryClient();
  const { data: employees } = useEmployeesQuery();
  const employee = employees?.find((e) => e.id === employeeId);
  const updateEmployee = useUpdateEmployeeMutation();
  const [basicData, setBasicData] = useState<BasicSectionData>({
    elements: [],
  });
  const isBusy = isLoading || !data || !itemTypes || !employee;

  // If basicData.elements is undefined, initialize with an empty array
  const elements = basicData.elements || [];

  // Reset and initialize basicData from fetched items once per payslip
  useEffect(() => {
    setBasicData({ elements: [] });
    initRef.current = false;
  }, [employeeId, periodId]);

  useEffect(() => {
    if (data && itemTypes && employee) {
      // Always load data from database, regardless of initRef state
      const elements = data.items
        .map((item) => {
          const typeMeta = itemTypes.find((t) => t.id === item.item_type_id);
          if (!typeMeta) return null;
          switch (typeMeta.code) {
            case "salary":
              return {
                id: item.id,
                type: "salary",
                amount: item.rate_or_amount,
                periodType,
                isRepeating: item.is_repeating,
                zeroizeNext: item.zeroise_next,
              } as BasicPayElement;
            case "daily": {
              const rate = item.rate_or_amount;
              const days = item.units || 0;
              const savedLabel = employee.dailyRates?.find(
                (r) => r.rate === rate && r.name !== "Standard daily rate",
              )?.name;
              return {
                id: item.id,
                type: "daily",
                rate,
                days,
                amount: rate * days,
                isRepeating: item.is_repeating,
                zeroizeNext: item.zeroise_next,
                rateSource: "selector" as const,
                rateLabel: savedLabel,
              } as BasicPayElement;
            }
            case "hourly": {
              const rate = item.rate_or_amount;
              const hours = item.units || 0;
              const savedLabel = employee.hourlyRates?.find(
                (r) => r.rate === rate && r.name !== "Standard hourly rate",
              )?.name;
              return {
                id: item.id,
                type: "hourly",
                rate,
                hours,
                amount: rate * hours,
                isRepeating: item.is_repeating,
                zeroizeNext: item.zeroise_next,
                rateSource: "selector" as const,
                rateLabel: savedLabel,
              } as BasicPayElement;
            }
            default:
              return null;
          }
        })
        .filter((e): e is BasicPayElement => e !== null);

      // Force update the state even if elements array looks the same
      setBasicData({ elements: [...elements] });
      initRef.current = true; // Mark as initialized after loading data
    } else if (data && itemTypes && employee && data.items?.length === 0) {
      // Explicitly handle empty items case
      setBasicData({ elements: [] });
      initRef.current = true;
    }
  }, [data, itemTypes, employee, periodType, employeeId, periodId]); // Added employeeId and periodId to dependencies

  const handleSaveRate = (
    type: "daily" | "hourly",
    _elementId: string,
    label: string,
    rate: number,
  ) => {
    if (!employee) return;
    const updatedEmp = {
      ...employee,
      dailyRates:
        type === "daily"
          ? [
              ...(employee.dailyRates || []),
              { name: label, rate, endDate: undefined },
            ]
          : employee.dailyRates,
      hourlyRates:
        type === "hourly"
          ? [
              ...(employee.hourlyRates || []),
              { name: label, rate, endDate: undefined },
            ]
          : employee.hourlyRates,
    };
    updateEmployee.mutate(updatedEmp);
  };

  const handleChange = (newData: BasicSectionData) => {
    // detect and delete removed items
    const prevIds = basicData.elements.map((e) => e.id);
    setBasicData(newData);
    const newIds = newData.elements.map((e) => e.id);
    const removedIds = prevIds.filter((id) => !newIds.includes(id));
    removedIds.forEach((id) => {
      del.mutate(id, {
        onSuccess: () => {
          if (dbPath)
            queryClient.invalidateQueries({
              queryKey: ["payslip", dbPath, employeeId, periodId],
            });
        },
      });
    });
    const payId = data?.payslip?.id;
    const upsertAll = (payslipId: string) => {
      newData.elements.forEach((el) => {
        const elementTempId = el.id;
        const typeMeta = itemTypes!.find((t) => t.code === el.type)!;
        const payload: any = {
          payslip_id: payslipId,
          item_type_id: typeMeta.id,
          units:
            el.type === "salary"
              ? undefined
              : el.type === "daily"
                ? el.days
                : el.hours,
          rate_or_amount: el.type === "salary" ? el.amount : el.rate,
          is_custom: false,
          is_repeating: el.isRepeating,
          zeroise_next: el.zeroizeNext,
        };
        // Use temp id prefix to detect new elements vs existing DB items
        const isTemp = el.id.startsWith(`${el.type}-`);
        if (!isTemp) {
          payload.id = el.id;
        }
        upsert.mutate(payload, {
          onSuccess: (lineItem) => {
            // On create, remap the UI element id to the real DB id
            if (!payload.id) {
              setBasicData((current) => ({
                elements: current.elements.map((e) =>
                  e.id === elementTempId ? { ...e, id: lineItem.id } : e,
                ),
              }));
            }
            if (dbPath)
              queryClient.invalidateQueries({
                queryKey: ["payslip", dbPath, employeeId, periodId],
              });
          },
        });
      });
    };
    if (!payId) {
      // Only create a payslip once: only invoke when idle
      if (createPayslip.status === "idle") {
        createPayslip.mutate(undefined, {
          onSuccess: (slip) => upsertAll(slip.id),
        });
      }
    } else {
      upsertAll(payId);
    }
  };

  // Handle input changes for a specific element
  const handleElementChange = (
    index: number,
    field: string,
    value: string | boolean | number,
  ) => {
    const newElements = [...elements];
    const element = { ...newElements[index] };

    // Type checking and conversion
    if (
      typeof value === "string" &&
      ["amount", "rate", "days", "hours"].includes(field)
    ) {
      (element as any)[field] = parseFloat(value) || 0;
    } else {
      (element as any)[field] = value;
    }

    // If isRepeating is set to false, also set zeroizeNext to false
    if (field === "isRepeating" && value === false) {
      element.zeroizeNext = false;
    }

    // Note: Amount calculation is now handled directly in the input handlers

    newElements[index] = element;
    handleChange({ ...basicData, elements: newElements });
  };

  // Add a new salary element
  const addSalaryElement = () => {
    const newElement: SalaryElement = {
      id: `salary-${Date.now()}`,
      type: "salary",
      amount: 0,
      periodType: periodType, // Use the provided period type
      isRepeating: true,
      zeroizeNext: false,
    };

    // Sort elements to maintain order: Salary, Daily, Hourly
    const newElements = [...elements, newElement].sort((a, b) => {
      const order = { salary: 0, daily: 1, hourly: 2 };
      return order[a.type] - order[b.type];
    });

    handleChange({ ...basicData, elements: newElements });
  };

  // Helper function to get the label for a preset multiplier
  const getMultiplierLabel = (multiplier: number): string => {
    const presetMultipliers = [
      { label: "Standard", value: 1 },
      { label: "1.25×", value: 1.25 },
      { label: "1.5×", value: 1.5 },
      { label: "2×", value: 2 },
      { label: "3×", value: 3 },
    ];

    const preset = presetMultipliers.find((p) => p.value === multiplier);
    return preset ? preset.label : "Custom";
  };

  // Add a new daily element
  const addDailyElement = () => {
    const newElement = {
      id: `daily-${Date.now()}`,
      type: "daily",
      rate: 0,
      days: 0,
      amount: 0, // Initialize calculated amount
      isRepeating: true,
      zeroizeNext: false,
      rateSource: "manual" as DailyElement["rateSource"], // Default to manual for new elements
    } as DailyElement;

    // Sort elements to maintain order: Salary, Daily, Hourly
    const newElements = [...elements, newElement].sort((a, b) => {
      const order = { salary: 0, daily: 1, hourly: 2 };
      return order[a.type] - order[b.type];
    });

    handleChange({ ...basicData, elements: newElements });
  };

  // Add a new hourly element
  const addHourlyElement = () => {
    const newElement = {
      id: `hourly-${Date.now()}`,
      type: "hourly",
      rate: 0,
      hours: 0,
      amount: 0, // Initialize calculated amount
      isRepeating: true,
      zeroizeNext: false,
      rateSource: "manual" as DailyElement["rateSource"], // Default to manual for new elements
    } as HourlyElement;

    // Sort elements to maintain order: Salary, Daily, Hourly
    const newElements = [...elements, newElement].sort((a, b) => {
      const order = { salary: 0, daily: 1, hourly: 2 };
      return order[a.type] - order[b.type];
    });

    handleChange({ ...basicData, elements: newElements });
  };

  // Remove an element
  const removeElement = (id: string) => {
    const newElements = elements.filter((element) => element.id !== id);
    handleChange({ ...basicData, elements: newElements });
  };

  // Clear all values (set to zero but keep elements)
  const clearAllValues = () => {
    const clearedElements = elements.map((element) => {
      if (element.type === "salary") {
        return { ...element, amount: 0 };
      } else if (element.type === "daily") {
        return { ...element, days: 0, rate: 0, amount: 0 };
      } else if (element.type === "hourly") {
        return { ...element, hours: 0, rate: 0, amount: 0 };
      }
      return element;
    });
    handleChange({ ...basicData, elements: clearedElements });
  };

  // Remove all elements
  const removeAllElements = () => {
    handleChange({ ...basicData, elements: [] });
  };

  // Group elements by type
  const salaryElements = elements.filter((e) => e.type === "salary");
  const dailyElements = elements.filter((e) => e.type === "daily");
  const hourlyElements = elements.filter((e) => e.type === "hourly");

  // derive base rates and saved rates from employee record
  const dailyBaseRate = employee?.dailyRates?.[0]?.rate ?? 0;
  const hourlyBaseRate = employee?.hourlyRates?.[0]?.rate ?? 0;
  const dailySavedRates =
    employee?.dailyRates
      ?.filter((r) => r.name !== "Standard daily rate")
      ?.map((r) => ({ label: r.name, value: r.rate })) ?? [];
  const hourlySavedRates =
    employee?.hourlyRates
      ?.filter((r) => r.name !== "Standard hourly rate")
      ?.map((r) => ({ label: r.name, value: r.rate })) ?? [];

  return isBusy ? (
    <BasicPaySectionSkeleton />
  ) : (
    <div>
      <SectionHeader
        title="Basic Pay"
        sectionType="basic"
        addButtons={[
          { label: "Salary", onClick: addSalaryElement },
          { label: "Daily", onClick: addDailyElement },
          { label: "Hourly", onClick: addHourlyElement },
        ]}
        actionButtons={[
          {
            label: "Clear Values",
            onClick: clearAllValues,
            variant: "outline",
          },
          {
            label: "Delete All",
            onClick: removeAllElements,
            variant: "destructive",
          },
        ]}
      />

      {/* Pay elements container */}
      <div className="min-h-[28px]">
        {/* Salary Elements */}
        <div className="space-y-1">
          {salaryElements.map((element) => {
            const elementIndex = elements.findIndex((e) => e.id === element.id);
            return (
              <div key={element.id} className="px-1.5">
                <div className="flex items-center">
                  <Button
                    variant="ghost"
                    size="icon"
                    className={getDisabledClassName(
                      "text-muted-foreground mr-3 h-5 w-5",
                    )}
                    onClick={() => removeElement(element.id)}
                    disabled={isReadOnly}
                  >
                    <Trash2 className="size-4" />
                  </Button>
                  <div className="flex-grow">
                    <div className="flex items-center">
                      <div className="ml-4 grid grid-cols-[148px_110px_98px_150px] items-center">
                        <span className="text-foreground pr-2 text-sm whitespace-nowrap">
                          {periodType} Pay
                        </span>
                        <PayslipNumberInput
                          id={`salary-${element.id}`}
                          className="h-7 w-full text-sm"
                          value={(element as SalaryElement).amount}
                          onChange={(value) =>
                            handleElementChange(
                              elementIndex,
                              "amount",
                              value ?? 0,
                            )
                          }
                          decimalPlaces={2}
                          allowCalculations={true}
                          currencySymbol=""
                        />
                        <span className="text-foreground pl-2 text-sm whitespace-nowrap"></span>
                        <div className="ml-4 flex items-center">
                          <RepeatingControl
                            id={element.id}
                            isChecked={element.isRepeating}
                            onChange={(checked) =>
                              handleElementChange(
                                elementIndex,
                                "isRepeating",
                                checked,
                              )
                            }
                          />

                          <ZeroizeControl
                            id={element.id}
                            isChecked={element.zeroizeNext}
                            onChange={(checked) =>
                              handleElementChange(
                                elementIndex,
                                "zeroizeNext",
                                checked,
                              )
                            }
                            disabled={!element.isRepeating}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Daily Elements */}
        <div className="mt-3 mb-3 space-y-0">
          {dailyElements.map((element) => {
            const elementIndex = elements.findIndex((e) => e.id === element.id);
            return (
              <div key={element.id} className="bg-card rounded-sm px-1.5">
                <div className="flex items-center">
                  <Button
                    variant="ghost"
                    size="icon"
                    className={getDisabledClassName(
                      "text-muted-foreground mr-3 h-5 w-5",
                    )}
                    onClick={() => removeElement(element.id)}
                    disabled={isReadOnly}
                  >
                    <Trash2 className="size-4" />
                  </Button>
                  <div className="flex-grow">
                    <div className="mb-1 flex items-center">
                      <div className="ml-4 grid grid-cols-[75px_65px_140px_60px_150px] items-center gap-1">
                        <div className="relative">
                          <PayslipNumberInput
                            id={`days-worked-${element.id}`}
                            className="h-7 w-full text-sm"
                            value={(element as DailyElement).days}
                            onChange={(value) => {
                              // Calculate the new amount immediately
                              const newValue = value ?? 0;
                              const rate = (element as DailyElement).rate;
                              const calculatedAmount = newValue * rate;

                              // Update both days and amount in one operation
                              const updatedElement = {
                                ...(element as DailyElement),
                                days: newValue,
                                amount: calculatedAmount,
                              };

                              const newElements = [...elements];
                              newElements[elementIndex] = updatedElement;
                              handleChange({
                                ...basicData,
                                elements: newElements,
                              });
                            }}
                            decimalPlaces={4}
                            allowCalculations={true}
                            onKeyDown={(e) => {
                              if (e.key === "Tab") return;
                              // Process calculation on every keystroke
                              setTimeout(() => {
                                const inputValue = (
                                  e.target as HTMLInputElement
                                ).value;
                                if (inputValue && inputValue !== "-") {
                                  try {
                                    // Try to parse the current input value
                                    const cleanedValue = inputValue.replace(
                                      /,/g,
                                      "",
                                    );
                                    const parsedValue =
                                      parseFloat(cleanedValue);

                                    if (!isNaN(parsedValue)) {
                                      // If it's a valid number, update and calculate
                                      const rate = (element as DailyElement)
                                        .rate;
                                      const calculatedAmount =
                                        parsedValue * rate;

                                      const updatedElement = {
                                        ...(element as DailyElement),
                                        days: parsedValue,
                                        amount: calculatedAmount,
                                      };

                                      const newElements = [...elements];
                                      newElements[elementIndex] =
                                        updatedElement;
                                      handleChange({
                                        ...basicData,
                                        elements: newElements,
                                      });
                                    }
                                  } catch (error) {
                                    // Ignore parsing errors during typing
                                  }
                                }
                              }, 0);
                            }}
                          />
                        </div>
                        <span className="px-2 text-sm whitespace-nowrap text-slate-600 dark:text-slate-300">
                          Days @
                        </span>
                        <div className="flex items-center">
                          <PayslipNumberInput
                            id={`daily-rate-${element.id}`}
                            className="h-7 w-full min-w-[110px] text-sm"
                            value={(element as DailyElement).rate}
                            onChange={(value) => {
                              // Calculate the new amount immediately
                              const newValue = value ?? 0;
                              const days = (element as DailyElement).days;
                              const calculatedAmount = days * newValue;

                              // Update both rate and amount in one operation
                              const updatedElement = {
                                ...(element as DailyElement),
                                rate: newValue,
                                amount: calculatedAmount,
                                // Mark this as a manual rate entry
                                rateSource:
                                  "manual" as DailyElement["rateSource"],
                              };

                              const newElements = [...elements];
                              newElements[elementIndex] = updatedElement;
                              handleChange({
                                ...basicData,
                                elements: newElements,
                              });
                            }}
                            decimalPlaces={2}
                            allowCalculations={true}
                            currencySymbol="£"
                            onKeyDown={(e) => {
                              if (e.key === "Tab") return;
                              // Process calculation on every keystroke
                              setTimeout(() => {
                                const inputValue = (
                                  e.target as HTMLInputElement
                                ).value;
                                if (inputValue && inputValue !== "-") {
                                  try {
                                    // Try to parse the current input value
                                    const cleanedValue = inputValue.replace(
                                      /,/g,
                                      "",
                                    );
                                    const parsedValue =
                                      parseFloat(cleanedValue);

                                    if (!isNaN(parsedValue)) {
                                      // If it's a valid number, update and calculate
                                      const days = (element as DailyElement)
                                        .days;
                                      const calculatedAmount =
                                        days * parsedValue;

                                      const updatedElement = {
                                        ...(element as DailyElement),
                                        rate: parsedValue,
                                        amount: calculatedAmount,
                                        // Mark this as a manual rate entry
                                        rateSource:
                                          "manual" as DailyElement["rateSource"],
                                      };

                                      const newElements = [...elements];
                                      newElements[elementIndex] =
                                        updatedElement;
                                      handleChange({
                                        ...basicData,
                                        elements: newElements,
                                      });
                                    }
                                  } catch (error) {
                                    // Ignore parsing errors during typing
                                  }
                                }
                              }, 0);
                            }}
                          />
                          <RateSelectorControl
                            id={element.id}
                            currentRate={(element as DailyElement).rate}
                            standardRate={dailyBaseRate}
                            savedRates={dailySavedRates}
                            onSaveRate={(label, rate) =>
                              handleSaveRate("daily", element.id, label, rate)
                            }
                            onChange={(rate, label) => {
                              // Calculate the new amount immediately
                              const days = (element as DailyElement).days;
                              const calculatedAmount = days * rate;

                              // Update both rate and amount in one operation
                              const updatedElement = {
                                ...(element as DailyElement),
                                rate: rate,
                                amount: calculatedAmount,
                                // Mark this as a selector rate entry
                                rateSource: "selector" as const,
                                // Store the rate label based on what was selected
                                rateLabel:
                                  label ||
                                  getMultiplierLabel(
                                    dailyBaseRate > 0
                                      ? rate / dailyBaseRate
                                      : 1,
                                  ),
                              };

                              const newElements = [...elements];
                              newElements[elementIndex] = updatedElement;
                              handleChange({
                                ...basicData,
                                elements: newElements,
                              });
                            }}
                          />
                        </div>
                        {/* Rate indicator */}
                        <span className="ml-1 text-xs font-normal whitespace-nowrap text-slate-600 dark:text-slate-300">
                          {(element as DailyElement).rate > 0 &&
                            ((element as DailyElement).rateSource === "manual"
                              ? "Custom"
                              : ((element as DailyElement).rateLabel ??
                                getMultiplierLabel(
                                  dailyBaseRate > 0
                                    ? (element as DailyElement).rate /
                                        dailyBaseRate
                                    : 1,
                                )))}
                        </span>
                        <div className="ml-4 flex items-center">
                          <RepeatingControl
                            id={element.id}
                            isChecked={element.isRepeating}
                            onChange={(checked) =>
                              handleElementChange(
                                elementIndex,
                                "isRepeating",
                                checked,
                              )
                            }
                          />

                          <ZeroizeControl
                            id={element.id}
                            isChecked={element.zeroizeNext}
                            onChange={(checked) =>
                              handleElementChange(
                                elementIndex,
                                "zeroizeNext",
                                checked,
                              )
                            }
                            disabled={!element.isRepeating}
                          />

                          {/* Only show amount if it's not zero */}
                          {(element as DailyElement).amount !== 0 &&
                            (element as DailyElement).amount !== undefined && (
                              <span className="ml-8 text-sm font-normal text-sky-700 dark:text-slate-300">
                                £
                                {Number(
                                  (element as DailyElement).amount,
                                ).toLocaleString("en-GB", {
                                  minimumFractionDigits: 2,
                                  maximumFractionDigits: 2,
                                })}
                              </span>
                            )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Hourly Elements */}
        <div className="mb-1">
          {hourlyElements.map((element) => {
            const elementIndex = elements.findIndex((e) => e.id === element.id);
            return (
              <div key={element.id} className="bg-card rounded-sm px-1.5">
                <div className="flex items-center">
                  <Button
                    variant="ghost"
                    size="icon"
                    className={getDisabledClassName(
                      "text-muted-foreground mr-3 h-5 w-5",
                    )}
                    onClick={() => removeElement(element.id)}
                    disabled={isReadOnly}
                  >
                    <Trash2 className="size-4" />
                  </Button>
                  <div className="flex-grow">
                    <div className="mb-1 flex items-center">
                      <div className="ml-4 grid grid-cols-[75px_65px_140px_60px_150px] items-center gap-1">
                        <div className="relative">
                          <PayslipNumberInput
                            id={`hours-worked-${element.id}`}
                            className="h-7 w-full text-sm"
                            value={(element as HourlyElement).hours}
                            onChange={(value) => {
                              // Calculate the new amount immediately
                              const newValue = value ?? 0;
                              const rate = (element as HourlyElement).rate;
                              const calculatedAmount = newValue * rate;

                              // Update both hours and amount in one operation
                              const updatedElement = {
                                ...(element as HourlyElement),
                                hours: newValue,
                                amount: calculatedAmount,
                              };

                              const newElements = [...elements];
                              newElements[elementIndex] = updatedElement;
                              handleChange({
                                ...basicData,
                                elements: newElements,
                              });
                            }}
                            decimalPlaces={4}
                            allowCalculations={true}
                            onKeyDown={(e) => {
                              if (e.key === "Tab") return;
                              // Process calculation on every keystroke
                              setTimeout(() => {
                                const inputValue = (
                                  e.target as HTMLInputElement
                                ).value;
                                if (inputValue && inputValue !== "-") {
                                  try {
                                    // Try to parse the current input value
                                    const cleanedValue = inputValue.replace(
                                      /,/g,
                                      "",
                                    );
                                    const parsedValue =
                                      parseFloat(cleanedValue);

                                    if (!isNaN(parsedValue)) {
                                      // If it's a valid number, update and calculate
                                      const rate = (element as HourlyElement)
                                        .rate;
                                      const calculatedAmount =
                                        parsedValue * rate;

                                      const updatedElement = {
                                        ...(element as HourlyElement),
                                        hours: parsedValue,
                                        amount: calculatedAmount,
                                      };

                                      const newElements = [...elements];
                                      newElements[elementIndex] =
                                        updatedElement;
                                      handleChange({
                                        ...basicData,
                                        elements: newElements,
                                      });
                                    }
                                  } catch (error) {
                                    // Ignore parsing errors during typing
                                  }
                                }
                              }, 0);
                            }}
                          />
                        </div>
                        <span className="px-2 text-sm whitespace-nowrap text-slate-600 dark:text-slate-300">
                          Hours @
                        </span>
                        <div className="flex items-center">
                          <PayslipNumberInput
                            id={`hourly-rate-${element.id}`}
                            className="h-7 w-full min-w-[110px] text-sm"
                            value={(element as HourlyElement).rate}
                            onChange={(value) => {
                              // Calculate the new amount immediately
                              const newValue = value ?? 0;
                              const hours = (element as HourlyElement).hours;
                              const calculatedAmount = hours * newValue;

                              // Update both rate and amount in one operation
                              const updatedElement = {
                                ...(element as HourlyElement),
                                rate: newValue,
                                amount: calculatedAmount,
                                // Mark this as a manual rate entry
                                rateSource:
                                  "manual" as DailyElement["rateSource"],
                              };

                              const newElements = [...elements];
                              newElements[elementIndex] = updatedElement;
                              handleChange({
                                ...basicData,
                                elements: newElements,
                              });
                            }}
                            decimalPlaces={2}
                            allowCalculations={true}
                            currencySymbol="£"
                            onKeyDown={(e) => {
                              if (e.key === "Tab") return;
                              // Process calculation on every keystroke
                              setTimeout(() => {
                                const inputValue = (
                                  e.target as HTMLInputElement
                                ).value;
                                if (inputValue && inputValue !== "-") {
                                  try {
                                    // Try to parse the current input value
                                    const cleanedValue = inputValue.replace(
                                      /,/g,
                                      "",
                                    );
                                    const parsedValue =
                                      parseFloat(cleanedValue);

                                    if (!isNaN(parsedValue)) {
                                      // If it's a valid number, update and calculate
                                      const hours = (element as HourlyElement)
                                        .hours;
                                      const calculatedAmount =
                                        hours * parsedValue;

                                      const updatedElement = {
                                        ...(element as HourlyElement),
                                        rate: parsedValue,
                                        amount: calculatedAmount,
                                        // Mark this as a manual rate entry
                                        rateSource:
                                          "manual" as DailyElement["rateSource"],
                                      };

                                      const newElements = [...elements];
                                      newElements[elementIndex] =
                                        updatedElement;
                                      handleChange({
                                        ...basicData,
                                        elements: newElements,
                                      });
                                    }
                                  } catch (error) {
                                    // Ignore parsing errors during typing
                                  }
                                }
                              }, 0);
                            }}
                          />
                          <RateSelectorControl
                            id={element.id}
                            currentRate={(element as HourlyElement).rate}
                            standardRate={hourlyBaseRate}
                            savedRates={hourlySavedRates}
                            onSaveRate={(label, rate) =>
                              handleSaveRate("hourly", element.id, label, rate)
                            }
                            onChange={(rate, label) => {
                              // Calculate the new amount immediately
                              const hours = (element as HourlyElement).hours;
                              const calculatedAmount = hours * rate;

                              // Update both rate and amount in one operation
                              const updatedElement = {
                                ...(element as HourlyElement),
                                rate: rate,
                                amount: calculatedAmount,
                                // Mark this as a selector rate entry
                                rateSource: "selector" as const,
                                // Store the rate label based on what was selected
                                rateLabel:
                                  label ||
                                  getMultiplierLabel(
                                    hourlyBaseRate > 0
                                      ? rate / hourlyBaseRate
                                      : 1,
                                  ),
                              };

                              const newElements = [...elements];
                              newElements[elementIndex] = updatedElement;
                              handleChange({
                                ...basicData,
                                elements: newElements,
                              });
                            }}
                          />
                        </div>
                        {/* Rate indicator */}
                        <span className="ml-1 text-xs font-normal whitespace-nowrap text-slate-600 dark:text-slate-300">
                          {(element as HourlyElement).rate > 0 &&
                            ((element as HourlyElement).rateSource === "manual"
                              ? "Custom"
                              : ((element as HourlyElement).rateLabel ??
                                getMultiplierLabel(
                                  hourlyBaseRate > 0
                                    ? (element as HourlyElement).rate /
                                        hourlyBaseRate
                                    : 1,
                                )))}
                        </span>
                        <div className="ml-4 flex items-center">
                          <RepeatingControl
                            id={element.id}
                            isChecked={element.isRepeating}
                            onChange={(checked) =>
                              handleElementChange(
                                elementIndex,
                                "isRepeating",
                                checked,
                              )
                            }
                          />

                          <ZeroizeControl
                            id={element.id}
                            isChecked={element.zeroizeNext}
                            onChange={(checked) =>
                              handleElementChange(
                                elementIndex,
                                "zeroizeNext",
                                checked,
                              )
                            }
                            disabled={!element.isRepeating}
                          />

                          {/* Only show amount if it's not zero */}
                          {(element as HourlyElement).amount !== 0 &&
                            (element as HourlyElement).amount !== undefined && (
                              <span className="ml-8 text-sm font-normal text-sky-700 dark:text-slate-300">
                                £
                                {Number(
                                  (element as HourlyElement).amount,
                                ).toLocaleString("en-GB", {
                                  minimumFractionDigits: 2,
                                  maximumFractionDigits: 2,
                                })}
                              </span>
                            )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
