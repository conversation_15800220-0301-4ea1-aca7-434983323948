"use client";

import * as React from "react";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { AlertCircle } from "lucide-react";
import { usePayslipReadOnly } from "@/providers/payslip-readonly-provider";

export interface PayslipNumberInputProps
  extends Omit<
    React.InputHTMLAttributes<HTMLInputElement>,
    "onChange" | "value" | "type"
  > {
  value: number | undefined | null;
  onChange: (value: number | undefined) => void;
  decimalPlaces?: number;
  min?: number;
  max?: number;
  allowNegative?: boolean;
  useThousandSeparator?: boolean;
  className?: string;
  allowCalculations?: boolean;
  onKeyDown?: React.KeyboardEventHandler<HTMLInputElement>;
  currencySymbol?: string; // Add support for currency symbol
}

export function PayslipNumberInput({
  value,
  onChange,
  decimalPlaces = 2,
  min,
  max,
  allowNegative = true,
  useThousandSeparator = true,
  className,
  onBlur,
  onKeyDown,
  allowCalculations = true,
  currencySymbol,
  disabled: externalDisabled,
  ...props
}: PayslipNumberInputProps) {
  // Get read-only state from context
  const { isReadOnly, getDisabledClassName } = usePayslipReadOnly();
  // Combine external disabled prop with read-only state
  const isDisabled = externalDisabled || isReadOnly;

  const [isEditing, setIsEditing] = React.useState(false);
  const [displayValue, setDisplayValue] = React.useState(
    value !== undefined && value !== null ? value.toFixed(decimalPlaces) : "",
  );
  const [calculationError, setCalculationError] = React.useState(false);
  const [errorMessage, setErrorMessage] = React.useState("");
  const lastProcessedValueRef = React.useRef<string | null>(null);
  const isProcessingBlurRef = React.useRef(false); // Flag to prevent useEffect interference during blur

  // Format with thousand separator for display
  const formatWithSeparator = React.useCallback(
    (value: number): string => {
      // First, get the full precision value with all decimal places
      const fullPrecisionValue = value.toFixed(decimalPlaces);

      // Determine how many decimal places to actually display
      // Always show at least 2 decimal places, but show more (up to decimalPlaces) if they're not zero
      let displayDecimalPlaces = 2; // Default to 2 decimal places

      if (decimalPlaces > 2) {
        // Check if there are non-zero digits after the 2nd decimal place
        const valueStr = value.toString();
        const decimalPointIndex = valueStr.indexOf(".");

        if (
          decimalPointIndex !== -1 &&
          valueStr.length > decimalPointIndex + 3
        ) {
          // Get the decimal part after the 2nd decimal place
          const extraDecimals = valueStr.substring(decimalPointIndex + 3);
          // If there are any non-zero digits, show all decimal places
          if (/[1-9]/.test(extraDecimals)) {
            displayDecimalPlaces = decimalPlaces;
          }
        }
      }

      // Format the value with the determined number of decimal places
      const fixedValue = value.toFixed(displayDecimalPlaces);

      if (!useThousandSeparator) return fixedValue;

      // Split into whole and decimal parts
      const parts = fixedValue.split(".");
      const wholePart = parts[0];
      const decimalPart = parts.length > 1 ? "." + parts[1] : "";

      // Add thousand separators to whole part
      const formattedWholePart = wholePart.replace(
        /\B(?=(\d{3})+(?!\d))/g,
        ",",
      );

      return formattedWholePart + decimalPart;
    },
    [decimalPlaces, useThousandSeparator],
  );

  // Update display value when prop value changes (if not editing)
  React.useEffect(() => {
    // Only update display value if we're not currently editing and not processing a blur event
    // This prevents race conditions during blur/tab navigation
    if (!isEditing && !isProcessingBlurRef.current) {
      if (value !== undefined && value !== null) {
        // Don't show 0.00 when the value is 0
        if (value === 0) {
          setDisplayValue("");
        } else {
          setDisplayValue(formatWithSeparator(value));
        }
      } else {
        setDisplayValue("");
      }
    }

    // Always sync ref when parent changes value, regardless of editing state
    lastProcessedValueRef.current =
      value !== undefined && value !== null ? value.toFixed(decimalPlaces) : "";
  }, [
    value,
    decimalPlaces,
    isEditing,
    useThousandSeparator,
    formatWithSeparator,
  ]);

  // Reset editing state when value prop changes significantly (e.g., switching employees)
  React.useEffect(() => {
    // If we're editing but the value changed externally, stop editing to allow update
    if (isEditing) {
      const currentFormattedValue =
        value !== undefined && value !== null ? formatWithSeparator(value) : "";
      const lastKnownValue = lastProcessedValueRef.current || "";

      // Only stop editing if the external value is significantly different AND
      // it's not just a formatting difference of the same numeric value
      const currentNumericValue =
        value !== undefined && value !== null ? value : 0;
      const displayNumericValue = displayValue
        ? parseFloat(displayValue.replace(/,/g, "")) || 0
        : 0;

      // If the external value is significantly different from what we last processed,
      // AND it's numerically different from what's currently displayed,
      // it's likely a data change (like switching employees), so stop editing
      if (
        currentFormattedValue !== lastKnownValue &&
        currentFormattedValue !== displayValue &&
        Math.abs(currentNumericValue - displayNumericValue) > 0.001 // Allow for small floating point differences
      ) {
        setIsEditing(false);
      }
    }
  }, [value, isEditing, displayValue, formatWithSeparator]);

  /**
   * Evaluates a mathematical expression string and returns the result
   * Supports +, -, *, /, and parentheses
   */
  const evaluateExpression = (expression: string): number => {
    // Remove all spaces and thousand separators
    const cleanedExpression = expression.replace(/\s/g, "").replace(/,/g, "");

    // Check if it's a simple number with no operators
    if (/^-?\d*\.?\d*$/.test(cleanedExpression)) {
      return parseFloat(cleanedExpression);
    }

    try {
      // Basic validation to prevent malicious code execution
      if (!/^[\d\+\-\*\/\(\)\.]+$/.test(cleanedExpression)) {
        throw new Error("Invalid characters in expression");
      }

      // Check for incomplete expressions (ending with an operator)
      if (/[\+\-\*\/]$/.test(cleanedExpression)) {
        throw new Error("Expression cannot end with an operator");
      }

      // Check for unbalanced parentheses
      const openParens = (cleanedExpression.match(/\(/g) || []).length;
      const closeParens = (cleanedExpression.match(/\)/g) || []).length;
      if (openParens !== closeParens) {
        throw new Error("Unbalanced parentheses in expression");
      }

      // Check for consecutive operators
      if (/[\+\-\*\/][\+\*\/]/.test(cleanedExpression)) {
        throw new Error("Expression contains consecutive operators");
      }

      // Use Function constructor to evaluate the expression safely
      // This is safer than eval() but still needs the input validation above
      let result;
      try {
        result = new Function(`return ${cleanedExpression}`)();
      } catch (syntaxError) {
        throw new Error("Invalid expression syntax");
      }

      // Verify the result is a valid number
      if (typeof result !== "number" || isNaN(result) || !isFinite(result)) {
        throw new Error("Expression did not evaluate to a valid number");
      }

      return result;
    } catch (error) {
      // If any error occurs during evaluation, throw it up
      throw error;
    }
  };

  // Function to process the input value (calculation or direct number)
  const processInputValue = (inputValue: string) => {
    try {
      // Reset previous error
      setCalculationError(false);
      setErrorMessage("");

      // Check if it's potentially a calculation
      if (
        (allowCalculations && /[\+\*\/\(\)]/.test(inputValue)) ||
        (inputValue.includes("-") && inputValue.lastIndexOf("-") > 0)
      ) {
        const result = evaluateExpression(inputValue);
        let finalValue = result;

        // Apply constraints after calculation
        if (min !== undefined && finalValue < min) finalValue = min;
        if (max !== undefined && finalValue > max) finalValue = max;
        if (!allowNegative && finalValue < 0) finalValue = 0;

        onChange(finalValue);
        setDisplayValue(formatWithSeparator(finalValue)); // Format result for display
        // **NEW**: Update ref with the successfully processed formatted value
        lastProcessedValueRef.current = formatWithSeparator(finalValue);
      } else {
        // Treat as a simple number
        const cleanedValue = inputValue.replace(/,/g, ""); // Remove thousand separators

        // Check for multiple decimal points
        if ((cleanedValue.match(/\./g) || []).length > 1) {
          throw new Error("Invalid number: Multiple decimal points");
        }

        // Handle single hyphen as zero
        if (cleanedValue === "-") {
          const zeroValue = 0;
          // Check constraints for zero
          if (!allowNegative && zeroValue < 0) {
            onChange(0); // Should already be 0, but for clarity
            setDisplayValue(formatWithSeparator(0));
          } else {
            onChange(zeroValue);
            setDisplayValue(formatWithSeparator(zeroValue));
          }
          return; // Exit processing early
        }

        const parsedValue = parseFloat(cleanedValue);
        if (!isNaN(parsedValue)) {
          let finalValue = parsedValue;
          if (min !== undefined && finalValue < min) finalValue = min;
          if (max !== undefined && finalValue > max) finalValue = max;
          if (!allowNegative && finalValue < 0) finalValue = 0;

          onChange(finalValue);
          setDisplayValue(formatWithSeparator(finalValue)); // Format for display
          // **NEW**: Update ref with the successfully processed formatted value
          lastProcessedValueRef.current = formatWithSeparator(finalValue);
        } else {
          // This case should now primarily catch invalid characters (non-numeric, non-calc)
          throw new Error("Invalid number");
        }
      }
    } catch (error) {
      // Handle all errors (calculation or invalid number)
      setCalculationError(true);
      const message = error instanceof Error ? error.message : "Invalid input";
      // Prepend the original input to the error message
      setErrorMessage(`"${inputValue}": ${message}`);
      setDisplayValue(inputValue); // Show the invalid input as entered
      onChange(undefined);
      // **NEW**: Keep the last successful value in the ref on error
      // lastProcessedValueRef.current = null; // Or keep the last known good value
    }
  };

  // **REVERT**: Back to minimal handleChange
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;

    // Update display immediately for responsiveness
    setDisplayValue(inputValue);

    // Reset error state if it was active
    if (calculationError) {
      setCalculationError(false);
      setErrorMessage("");
    }

    // Only handle the edge case of clearing the input or starting a negative number
    // to ensure the underlying value becomes undefined immediately for the parent.
    if (inputValue === "" || inputValue === "-") {
      onChange(undefined);
    }
    // No other parsing or onChange calls here.
  };

  // **NEW**: Centralized logic for processing value on blur/enter
  const handleValueProcessing = (currentInputValue: string) => {
    if (!currentInputValue) {
      // If empty on blur, set to 0
      const zeroValue = 0;
      const formattedZero = "0.00";
      onChange(zeroValue);
      setDisplayValue(""); // Clear display for zero values
      lastProcessedValueRef.current = formattedZero;
    } else if (currentInputValue === "-") {
      // If just hyphen on blur, treat as 0
      const zeroValue = 0;
      const formattedZero = "0.00";
      onChange(zeroValue);
      setDisplayValue(""); // Clear display for zero values
      lastProcessedValueRef.current = formattedZero;
    } else {
      // Store the input value in the ref immediately to prevent reversion
      lastProcessedValueRef.current = currentInputValue;
      // Always process the input value to ensure it's properly formatted
      processInputValue(currentInputValue);
    }
  };

  const handleFocus = () => setIsEditing(true);

  // Refactored: Use centralized processing logic
  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    // Store the current input value before any async operations
    const currentInputValue = e.target.value;

    // Set flag to prevent useEffect from interfering during blur processing
    isProcessingBlurRef.current = true;

    // Process the value immediately and update the ref to prevent reversion
    if (
      currentInputValue === "" ||
      currentInputValue === "0" ||
      currentInputValue === "0.00"
    ) {
      setDisplayValue("");
      onChange(0); // Still set the value to 0 for the data model
      lastProcessedValueRef.current = "0.00";
    } else {
      handleValueProcessing(currentInputValue);
    }

    // Set editing to false after processing
    setIsEditing(false);

    // Clear the processing flag after a delay
    setTimeout(() => {
      isProcessingBlurRef.current = false;
    }, 100); // Longer delay to ensure all parent updates are complete

    // Call original onBlur prop if provided
    if (onBlur) onBlur(e);
  };

  // Refactored: Use centralized processing logic
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault(); // Prevent default form submission or other actions

      // Store the current input value
      const currentInputValue = e.currentTarget.value;

      // If the field is empty or just 0, keep it empty
      if (
        currentInputValue === "" ||
        currentInputValue === "0" ||
        currentInputValue === "0.00"
      ) {
        setDisplayValue("");
        onChange(0); // Still set the value to 0 for the data model
      } else {
        handleValueProcessing(currentInputValue);
      }
    }
    // Call original onKeyDown prop if provided
    if (onKeyDown) onKeyDown(e);
  };

  return (
    <div className="relative">
      {currencySymbol && (
        <div className="absolute top-0.5 bottom-0.5 left-0.5 flex w-6 items-center justify-center rounded-l-sm border-r border-slate-300 bg-slate-300 dark:border-slate-600 dark:bg-slate-300">
          <span className="text-sm font-normal text-slate-700">
            {currencySymbol}
          </span>
        </div>
      )}
      <Input
        type="text" // Use text to allow calculation input
        inputMode="decimal" // Hint for mobile keyboards
        value={displayValue} // Always bind to internal displayValue state
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown} // Add keydown handler
        placeholder=""
        disabled={isDisabled}
        className={cn(
          "rounded-sm", // Ensure consistent padding
          currencySymbol ? "pr-2 pl-7" : "px-2",
          calculationError ? "border-red-500 focus-visible:ring-red-500" : "",
          getDisabledClassName(className), // Apply disabled styling from context
        )}
        style={{
          height: props.style?.height || "1.4rem", // Default to h-7 equivalent (1.75rem)
          minHeight: props.style?.minHeight || "1rem",
        }}
        {...props}
      />
      {calculationError && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="absolute top-1/2 right-2 -translate-y-1/2 cursor-help">
                <AlertCircle className="h-4 w-4 text-red-500" />
              </div>
            </TooltipTrigger>
            <TooltipContent
              side="top"
              className="max-w-xs border-red-600 bg-gradient-to-b from-red-400 to-red-700 text-white"
            >
              <p className="text-sm">{errorMessage}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
  );
}
