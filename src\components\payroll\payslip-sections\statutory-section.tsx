"use client";

import React from "react";
import { Calendar } from "lucide-react";
import { SectionHeader } from "@/components/payroll/payslip-controls";
import { usePayslipReadOnly } from "@/providers/payslip-readonly-provider";

interface StatutoryPayData {
  ssp: number; // Statutory Sick Pay
  smp: number; // Statutory Maternity Pay
  spp: number; // Statutory Paternity Pay
  shpp: number; // Statutory Shared Parental Pay
}

interface PayslipStatutorySectionProps {
  data: StatutoryPayData;
  onChange: (data: StatutoryPayData) => void;
}

const PayslipStatutorySection: React.FC<PayslipStatutorySectionProps> = ({
  data,
  onChange,
}) => {
  // Get read-only state from context
  const { isReadOnly } = usePayslipReadOnly();

  // Handle opening the calendar
  const handleOpenCalendar = () => {
    // This will be implemented later when calendar functionality is added
    console.log("Opening statutory pay calendar");
  };

  return (
    <div className="space-y-2">
      <SectionHeader
        title="Statutory Pay"
        actionButtons={
          !isReadOnly
            ? [
                {
                  label: "Open Calendar",
                  onClick: handleOpenCalendar,
                  variant: "outline",
                  icon: <Calendar className="h-4 w-4" />,
                },
              ]
            : []
        }
      />

      {/* Placeholder message for statutory pay */}
      <div className="rounded-lg bg-slate-50 p-4 text-center text-sm text-slate-500 dark:bg-zinc-800 dark:text-slate-400">
        Use the calendar button to add statutory pay elements
      </div>
    </div>
  );
};

export default PayslipStatutorySection;
