import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import type { Employer } from "@/lib/schemas/employer";

// Use the preload-exposed API for DB access
const getEmployerIPC = (dbPath: string) => {
  // @ts-expect-error - window.api is exposed by preload script
  return window.api.getEmployer(dbPath);
};
const updateEmployerIPC = (dbPath: string, employer: Employer) => {
  // @ts-expect-error - window.api is exposed by preload script
  return window.api.updateEmployer(dbPath, employer);
};

/**
 * TanStack Query hook to fetch employer data by dbPath (active employer DB).
 * @param dbPath Employer DB file path
 */
export function useEmployerQuery(dbPath: string | undefined) {
  return useQuery<Employer, Error>({
    queryKey: ["employer", dbPath],
    queryFn: async () => {
      if (!dbPath) throw new Error("No employer DB path provided");
      return await getEmployerIPC(dbPath);
    },
    enabled: !!dbPath,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}

/**
 * TanStack Query mutation for updating employer data.
 * @param dbPath Employer DB file path
 */
export function useUpdateEmployerMutation(dbPath: string | undefined) {
  const queryClient = useQueryClient();
  return useMutation<Employer, Error, Employer>({
    mutationFn: async (employer: Employer) => {
      if (!dbPath) throw new Error("No employer DB path provided");
      return await updateEmployerIPC(dbPath, employer);
    },
    onSuccess: () => {
      if (dbPath) {
        queryClient.invalidateQueries({ queryKey: ["employer", dbPath] });
      }
    },
  });
}
