"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { SquarePlus, SquareMinus } from "lucide-react";
import { usePayslipReadOnly } from "@/providers/payslip-readonly-provider";

type SectionType = "basic" | "additions" | "deductions";

interface AddElementButtonProps {
  label: string;
  onClick: () => void;
  className?: string;
  sectionType?: SectionType;
  disabled?: boolean;
}

export const AddElementButton: React.FC<AddElementButtonProps> = ({
  label,
  onClick,
  className = "",
  sectionType = "basic",
  disabled: externalDisabled,
}) => {
  // Get read-only state from context
  const { isReadOnly, getDisabledClassName } = usePayslipReadOnly();

  // Combine external disabled prop with read-only state
  const isDisabled = externalDisabled || isReadOnly;
  // Base styles for all buttons
  const baseStyles = "h-6 text-sm font-normal dark:hover:bg-zinc-700";

  // Section-specific styles
  const sectionStyles = {
    basic: "text-slate-600 dark:text-slate-200",
    additions: "text-slate-600 dark:text-slate-200",
    deductions: "text-slate-600 dark:text-slate-200",
  };

  // Icon colors for each section
  const iconColors = {
    basic: "text-sky-500/70 dark:text-sky-400",
    additions: "text-emerald-500/80 dark:text-emerald-300",
    deductions: "text-red-500/70 dark:text-red-400",
  };

  // Use different icon based on section type
  const Icon = sectionType === "deductions" ? SquareMinus : SquarePlus;

  return (
    <Button
      onClick={onClick}
      size="sm"
      variant="outline"
      disabled={isDisabled}
      className={getDisabledClassName(
        `${baseStyles} ${sectionStyles[sectionType]} ${className}`,
      )}
    >
      <Icon className={`size-4 ${iconColors[sectionType]}`} />
      {label}
    </Button>
  );
};
