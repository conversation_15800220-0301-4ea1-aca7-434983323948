import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { PAY_PERIOD_TYPES } from "@/drizzle/schema/employer/payPeriod";
import { useTaxYear } from "@/providers/tax-year-provider";
import {
  useCreatePayPeriodMutation,
  useCreatePayPeriodsMutation,
  useCreatePayPeriodScheduleAndPeriodsMutation,
} from "@/hooks/tanstack-query/usePayPeriods";
import { z } from "zod";

const WEEKDAYS = [
  "Saturday",
  "Sunday",
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
];
// For UI dropdowns (Monday first, UK convention)
const WEEKDAYS_DROPDOWN = [
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
  "Sunday",
];
const PERIOD_DATES = Array.from({ length: 28 }, (_, i) => (i + 1).toString());

interface PayrollWizardModalProps {
  open: boolean;
  payFrequency: (typeof PAY_PERIOD_TYPES)[number] | null;
  onClose: () => void;
}

export const PayrollWizardModal: React.FC<PayrollWizardModalProps> = ({
  open,
  payFrequency,
  onClose,
}) => {
  const { taxYear } = useTaxYear();
  console.log("[PayrollWizardModal] render", { open, payFrequency });
  // --- Resettable state ---
  const [label, setLabel] = useState("");
  const [periodEndDay, setPeriodEndDay] = useState<string>("Friday");
  const [periodEndDate, setPeriodEndDate] = useState<string>("28");
  const [periodEndIsLast, setPeriodEndIsLast] = useState(false);
  const [payDateRuleType, setPayDateRuleType] = useState<string>("PED");
  const [payDateWeekday, setPayDateWeekday] = useState<string>("Friday");
  const [payDateWhichMonth, setPayDateWhichMonth] = useState<string>("this");
  const [payDateParticularDate, setPayDateParticularDate] =
    useState<string>("28");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Reset all wizard state
  function resetState() {
    setLabel("");
    setPeriodEndDay("Friday");
    setPeriodEndDate("28");
    setPeriodEndIsLast(false);
    setPayDateRuleType("PED");
    setPayDateWeekday("Friday");
    setPayDateWhichMonth("this");
    setPayDateParticularDate("28");
    setIsSubmitting(false);
    setError(null);
  }

  // Wrap onClose to always reset state first
  function handleClose() {
    console.log("[PayrollWizardModal] handleClose called");
    resetState();
    onClose();
  }

  const createSingleMutation = useCreatePayPeriodMutation();
  const createScheduleAndPeriodsMutation =
    useCreatePayPeriodScheduleAndPeriodsMutation();
  if (!open || !payFrequency) return null; // Already guarded in parent
  // Type assertion to ensure payFrequency is always a valid string below
  const safePayFrequency = payFrequency as (typeof PAY_PERIOD_TYPES)[number];

  // Build period end UI
  let periodEndInput = null;
  if (["weekly", "two_weekly", "four_weekly"].includes(payFrequency)) {
    periodEndInput = (
      <Select value={periodEndDay} onValueChange={setPeriodEndDay}>
        <SelectTrigger className="mb-2 w-full">
          <SelectValue placeholder="Select period end day" />
        </SelectTrigger>
        <SelectContent>
          {WEEKDAYS.map((d) => (
            <SelectItem key={d} value={d}>
              {d}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    );
  } else {
    periodEndInput = (
      <div className="mb-2 flex items-center gap-2">
        <Select
          value={periodEndDate}
          onValueChange={(v) => {
            setPeriodEndDate(v);
            setPeriodEndIsLast(false);
          }}
        >
          <SelectTrigger className="w-32">
            <SelectValue placeholder="End date" />
          </SelectTrigger>
          <SelectContent>
            {PERIOD_DATES.map((d) => (
              <SelectItem key={d} value={d}>
                {d}
              </SelectItem>
            ))}
            <SelectItem value="last">Last day of month</SelectItem>
          </SelectContent>
        </Select>
      </div>
    );
  }

  // Build pay date rule UI
  const payDateRuleOptions = [
    { value: "PED", label: "Period End Date (PED)" },
    { value: "weekday_on_or_before_PED", label: "Weekday on or before PED" },
    { value: "weekday_on_or_after_PED", label: "Weekday on or after PED" },
  ];
  if (["weekly"].includes(payFrequency)) {
    payDateRuleOptions.push(
      { value: "weekday_after_PED", label: "Particular weekday after PED" },
      { value: "weekday_before_PED", label: "Particular weekday before PED" },
    );
  }
  if (["monthly", "quarterly", "yearly"].includes(payFrequency)) {
    payDateRuleOptions.push(
      {
        value: "particular_date_this_month",
        label: "Particular date (this month)",
      },
      {
        value: "particular_date_next_month",
        label: "Particular date (next month)",
      },
      { value: "last_working_day", label: "Last working day (Mon-Fri)" },
      { value: "last_day", label: "Last day of month" },
    );
  }

  // Helper function to format dates from ISO format (YYYY-MM-DD) to DD-MM-YYYY
  function formatDateToDDMMYYYY(isoDateString: string): string {
    const date = new Date(isoDateString);
    return date.toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  }

  /**
   * Normalize a Date to local noon to avoid DST/UTC shifts.
   */
  function normalizeDate(date: Date): Date {
    return new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate(),
      12, // local noon
      0,
      0,
      0,
    );
  }

  // Helper function to convert pay period type to proper label
  function formatPayPeriodTypeToLabel(
    type: (typeof PAY_PERIOD_TYPES)[number],
  ): string {
    switch (type) {
      case "weekly":
        return "Weekly";
      case "two_weekly":
        return "2-Weekly";
      case "four_weekly":
        return "4-Weekly";
      case "monthly":
        return "Monthly";
      case "quarterly":
        return "Quarterly";
      case "yearly":
        return "Yearly";
      default:
        return String(type).charAt(0).toUpperCase() + String(type).slice(1);
    }
  }

  // Helper: format Date to local YYYY-MM-DD to prevent UTC offset issues
  function formatLocalDate(date: Date): string {
    const yyyy = date.getFullYear();
    const mm = String(date.getMonth() + 1).padStart(2, "0");
    const dd = String(date.getDate()).padStart(2, "0");
    return `${yyyy}-${mm}-${dd}`;
  }

  // Build pay date rule JSON
  function buildPayDateRule() {
    switch (payDateRuleType) {
      case "PED":
        return { type: "PED" };
      case "weekday_on_or_before_PED":
      case "weekday_on_or_after_PED":
      case "weekday_after_PED":
      case "weekday_before_PED":
        return { type: payDateRuleType, weekday: payDateWeekday };
      case "particular_date_this_month":
      case "particular_date_next_month":
        return {
          type: payDateRuleType,
          date: payDateParticularDate,
          whichMonth: payDateWhichMonth,
        };
      case "last_working_day":
        return { type: "last_working_day" };
      case "last_day":
        return { type: "last_day" };
      default:
        return { type: payDateRuleType };
    }
  }

  // Utility: Parse tax year string (e.g. '2024/25') to get start and end years
  function parseTaxYear(taxYear: string): {
    startYear: number;
    endYear: number;
  } {
    // Expecting format 'YYYY/YY' or 'YYYY/YYYY'
    const match = taxYear.match(/^(\d{4})\/(\d{2,4})$/);
    if (!match) throw new Error(`Invalid tax year format: ${taxYear}`);
    const startYear = parseInt(match[1], 10);
    let endYear = parseInt(match[2], 10);
    if (endYear < 100) endYear += 2000; // handle '24' as 2024
    return { startYear, endYear };
  }

  // Helper: get the next period's start/end dates and pay date for each frequency
  function generatePayPeriodsForTaxYear({
    frequency,
    periodEndDay,
    periodEndDate,
    payDateRule,
    taxYear,
  }: {
    frequency: string;
    periodEndDay: string;
    periodEndDate: string;
    payDateRule: any;
    taxYear: string;
  }) {
    console.log("=== PAY PERIOD LOGGING ACTIVE ===");
    // Tax year runs from 6 April startYear to 5 April endYear
    const { startYear, endYear } = parseTaxYear(taxYear);
    const taxYearStart = new Date(startYear, 3, 6); // 6 April
    const taxYearEnd = new Date(endYear, 3, 5); // 5 April next year
    const periods = [];
    let periodEnd: Date;
    let periodStart: Date;
    let numPeriods = 0;
    let periodLabel = "";
    let periodAdvanceDays = 0;
    let periodAdvanceMonths = 0; // Only one declaration of periodStart allowed
    switch (frequency) {
      case "monthly":
        numPeriods = 12;
        periodLabel = "Month";
        periodAdvanceMonths = 1;
        break;
      case "four_weekly":
        numPeriods = 13;
        periodLabel = "4-Week";
        periodAdvanceDays = 28;
        break;
      case "two_weekly":
        numPeriods = 26;
        periodLabel = "2-Week";
        periodAdvanceDays = 14;
        break;
      case "weekly":
        numPeriods = 53; // Will trim if pay date falls outside tax year
        periodLabel = "Week";
        periodAdvanceDays = 7;
        break;
      case "quarterly":
        numPeriods = 4;
        periodLabel = "Quarter";
        periodAdvanceMonths = 3;
        break;
      case "yearly":
        numPeriods = 1;
        periodLabel = "Year";
        periodAdvanceMonths = 12;
        break;
      default:
        break;
    }
    // Helper to get the next periodEnd date for each period
    function getNextPeriodEnd(prevEnd: Date, i: number): Date {
      if (["monthly", "quarterly", "yearly"].includes(frequency)) {
        const d = new Date(prevEnd);
        if (periodEndDate === "last") {
          // Last day of next period's month
          d.setMonth(prevEnd.getMonth() + periodAdvanceMonths + 1, 0);
        } else {
          d.setMonth(prevEnd.getMonth() + periodAdvanceMonths);
          d.setDate(parseInt(periodEndDate, 10));
          // If date overflows, JS auto-fixes to next month, so clamp
          if (
            d.getMonth() !==
            (prevEnd.getMonth() + periodAdvanceMonths) % 12
          ) {
            d.setDate(0);
          }
        }
        return d;
      } else {
        // Weekly, 2-weekly, 4-weekly
        const d = new Date(prevEnd);
        d.setDate(d.getDate() + periodAdvanceDays);
        return d;
      }
    }
    // Calculate first period end and start for monthly/quarterly/yearly
    if (["monthly", "quarterly", "yearly"].includes(frequency)) {
      // For monthly: first period end is selected date/last day of April, start is 1st April or day after previous month's end
      const currentMonth = taxYearStart.getMonth();
      const currentYear = taxYearStart.getFullYear();
      // First period end
      if (periodEndDate === "last") {
        periodEnd = new Date(currentYear, currentMonth + 1, 0); // last day of April
      } else {
        periodEnd = new Date(
          currentYear,
          currentMonth,
          parseInt(periodEndDate, 10),
        );
        // Clamp if overflow
        if (periodEnd.getMonth() !== currentMonth) {
          periodEnd = new Date(currentYear, currentMonth + 1, 0);
        }
      }
      // First period start
      if (periodEndDate === "last") {
        periodStart = new Date(currentYear, currentMonth, 1);
      } else {
        // Start is day after previous month's selected end date
        let prevMonth = currentMonth - 1;
        let prevYear = currentYear;
        if (prevMonth < 0) {
          prevMonth = 11;
          prevYear--;
        }
        const prevEndDay =
          periodEndDate === "last"
            ? new Date(prevYear, prevMonth + 1, 0).getDate()
            : parseInt(periodEndDate, 10);
        periodStart = new Date(prevYear, prevMonth, prevEndDay + 1);
      }
    } else {
      // Weekly types: find first selected weekday on/after tax year start
      periodEnd = new Date(taxYearStart);
      const targetDay = WEEKDAYS.indexOf(periodEndDay);
      while (periodEnd.getDay() !== targetDay) {
        periodEnd.setDate(periodEnd.getDate() + 1);
      }
      // For weekly/four-weekly/two-weekly, period start is periodEnd minus (periodAdvanceDays - 1)
      periodStart = new Date(periodEnd);
      periodStart.setDate(periodEnd.getDate() - (periodAdvanceDays - 1));
    }

    const minPayDate = new Date(
      taxYearStart.getFullYear(),
      taxYearStart.getMonth(),
      taxYearStart.getDate(),
    ); // start of tax year
    // Helper to get next period's start and end
    function getNextPeriod(periodStart: Date, periodEnd: Date, idx: number) {
      const nextEnd = getNextPeriodEnd(periodEnd, idx);
      let nextStart: Date;
      if (["monthly", "quarterly", "yearly"].includes(frequency)) {
        nextStart = new Date(nextEnd);
        nextStart.setDate(1);
      } else {
        nextStart = new Date(nextEnd);
      }
      return { nextStart, nextEnd };
    }
    // Advance to first period that overlaps the tax year
    while (periodEnd.getTime() < taxYearStart.getTime()) {
      const { nextStart, nextEnd } = getNextPeriod(periodStart, periodEnd, 0);
      periodStart = nextStart;
      periodEnd = nextEnd;
    }
    let validPeriods = 0;
    let periodIndex = 0;
    while (validPeriods < numPeriods) {
      // Calculate period start
      if (["monthly", "quarterly", "yearly"].includes(frequency)) {
        if (periods.length === 0) {
          // Already set above
        } else {
          periodStart = new Date(periods[periods.length - 1].period_end);
          periodStart.setDate(periodStart.getDate() + 1);
        }
      } else {
        if (periods.length === 0) {
          // Already set above
        } else {
          periodStart = new Date(periods[periods.length - 1].period_end);
          periodStart.setDate(periodStart.getDate() + 1);
        }
      }
      // Calculate pay date using pay date rule
      let payDate = new Date(periodEnd);
      if (payDateRule && payDateRule.type) {
        switch (payDateRule.type) {
          case "PED":
            payDate = new Date(periodEnd);
            break;
          case "weekday_on_or_after_PED": {
            const targetDay = WEEKDAYS.indexOf(payDateRule.weekday);
            payDate = new Date(periodEnd);
            while (payDate.getDay() !== targetDay) {
              payDate.setDate(payDate.getDate() + 1);
            }
            break;
          }
          case "weekday_on_or_before_PED": {
            const targetDay = WEEKDAYS.indexOf(payDateRule.weekday);
            payDate = new Date(periodEnd);
            while (payDate.getDay() !== targetDay) {
              payDate.setDate(payDate.getDate() - 1);
            }
            break;
          }
          case "last_working_day": {
            const d = new Date(periodEnd);
            while (d.getDay() === 0 || d.getDay() === 6) {
              d.setDate(d.getDate() - 1);
            }
            payDate = d;
            break;
          }
          case "last_day":
            payDate = new Date(periodEnd);
            break;
          case "particular_date_this_month": {
            payDate = new Date(periodEnd);
            payDate.setDate(parseInt(payDateRule.date, 10));
            break;
          }
          case "particular_date_next_month": {
            // Next month, preserve date-first to avoid overflow
            const d = new Date(periodEnd);
            d.setDate(parseInt(payDateRule.date, 10));
            d.setMonth(d.getMonth() + 1);
            payDate = d;
            break;
          }
          default:
            payDate = new Date(periodEnd);
        }
      }
      // Stop when pay date is outside tax year
      if (payDate > taxYearEnd) break;
      // Skip periods with pay date before tax year start
      if (
        normalizeDate(payDate).getTime() < normalizeDate(taxYearStart).getTime()
      ) {
        const nextEnd = getNextPeriodEnd(periodEnd, periodIndex);
        if (["weekly", "two_weekly", "four_weekly"].includes(frequency)) {
          periodStart = new Date(nextEnd);
          periodStart.setDate(nextEnd.getDate() - (periodAdvanceDays - 1));
          periodEnd = nextEnd;
        } else {
          // Advance monthly/quarterly/yearly by 1 day after previous period end
          const nextStart = new Date(periodEnd);
          nextStart.setDate(periodEnd.getDate() + 1);
          periodStart = nextStart;
          periodEnd = nextEnd;
        }
        periodIndex++;
        continue;
      }
      // Determine period end
      const thisPeriodEnd = new Date(periodEnd);
      // Calculate period start
      if (["monthly", "quarterly", "yearly"].includes(frequency)) {
        if (periods.length === 0) {
          // Already set above
        } else {
          periodStart = new Date(periods[periods.length - 1].period_end);
          periodStart.setDate(periodStart.getDate() + 1);
        }
      } else {
        if (periods.length === 0) {
          // Already set above
        } else {
          periodStart = new Date(periods[periods.length - 1].period_end);
          periodStart.setDate(periodStart.getDate() + 1);
        }
      }
      periods.push({
        type: frequency,
        start_day: 0,
        end_day: 0,
        pay_date_rule: payDateRule,
        period_start: formatLocalDate(periodStart),
        period_end: formatLocalDate(thisPeriodEnd),
        pay_date: formatLocalDate(payDate),
        tax_period_label: `${periodLabel} ${validPeriods + 1}`,
        active: true,
        notes: null,
        period_number: validPeriods + 1,
        tax_year: taxYear,
      });
      // Advance periodEnd for next period
      periodEnd = getNextPeriodEnd(periodEnd, periodIndex);
      validPeriods++;
      periodIndex++;
    }
    return periods;
  }

  // Submission handler
  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    try {
      // --- Validation for label field (optional, max 20 chars if provided) ---
      // Use pay period type as default label if none provided
      const finalLabel =
        label.trim() || formatPayPeriodTypeToLabel(safePayFrequency);

      if (finalLabel.length > 20) {
        setError("Schedule label must be at most 20 characters.");
        setIsSubmitting(false);
        return;
      }
      let end_day = 1;
      if (["weekly", "two_weekly", "four_weekly"].includes(safePayFrequency)) {
        end_day = WEEKDAYS.indexOf(periodEndDay);
      } else {
        end_day = periodEndDate === "last" ? 99 : parseInt(periodEndDate, 10);
      }
      const pay_date_rule = buildPayDateRule();
      // Validate single period for schema compliance
      const schema = z.object({
        label: z.string().min(1).max(20),
        type: z.string(),
        end_day: z.number(),
        pay_date_rule: z.any(),
      });
      schema.parse({
        label: finalLabel,
        type: safePayFrequency,
        end_day,
        pay_date_rule,
      });
      // Generate all periods for the tax year
      const periods = generatePayPeriodsForTaxYear({
        frequency: safePayFrequency,
        periodEndDay,
        periodEndDate,
        payDateRule: pay_date_rule,
        taxYear,
      });
      // Create schedule and all periods in one mutation
      await createScheduleAndPeriodsMutation.mutateAsync({
        label: finalLabel,
        type: safePayFrequency,
        tax_year: taxYear,
        periods,
      });
      handleClose();
    } catch (err: any) {
      setError(err.message || "Validation error");
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Dialog
      open={open}
      onOpenChange={(val) => {
        if (!val) handleClose();
      }}
    >
      <DialogContent className="max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Setup Pay Period Schedule</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="mb-1 block">Schedule Name (optional)</label>
            <Input
              value={label}
              onChange={(e) => {
                setLabel(e.target.value);
                if (error) setError(null);
              }}
              placeholder="e.g. Office Staff"
              maxLength={20}
            />
            <div className="text-muted-foreground flex justify-between text-xs">
              <span>Max 20 characters</span>
              <span>{label.length}/20</span>
            </div>
            {error && <div className="mt-1 text-xs text-red-600">{error}</div>}
          </div>
          <div>
            <label className="mb-1 block">
              Period End{" "}
              {["weekly", "two_weekly", "four_weekly"].includes(payFrequency)
                ? "Day"
                : "Date"}
            </label>
            {/* Render Period End Day dropdown with Monday first if weekly type */}
            {["weekly", "two_weekly", "four_weekly"].includes(payFrequency) ? (
              <Select value={periodEndDay} onValueChange={setPeriodEndDay}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Weekday" />
                </SelectTrigger>
                <SelectContent>
                  {WEEKDAYS_DROPDOWN.map((d) => (
                    <SelectItem key={d} value={d}>
                      {d}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            ) : (
              periodEndInput
            )}
          </div>
          <div>
            <label className="mb-1 block">Pay Date Rule</label>
            <RadioGroup
              value={payDateRuleType}
              onValueChange={setPayDateRuleType}
              className="mb-2"
            >
              {payDateRuleOptions.map((opt) => (
                <div
                  key={opt.value}
                  className="mb-1 flex items-center space-x-2"
                >
                  <RadioGroupItem value={opt.value} id={opt.value} />
                  <label htmlFor={opt.value}>{opt.label}</label>
                  {/* Show extra selectors for some rules */}
                  {[
                    "weekday_on_or_before_PED",
                    "weekday_on_or_after_PED",
                    "weekday_after_PED",
                    "weekday_before_PED",
                  ].includes(opt.value) &&
                    payDateRuleType === opt.value && (
                      <Select
                        value={payDateWeekday}
                        onValueChange={setPayDateWeekday}
                      >
                        <SelectTrigger className="ml-2 w-32">
                          <SelectValue placeholder="Weekday" />
                        </SelectTrigger>
                        <SelectContent>
                          {WEEKDAYS_DROPDOWN.map((d) => (
                            <SelectItem key={d} value={d}>
                              {d}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                  {[
                    "particular_date_this_month",
                    "particular_date_next_month",
                  ].includes(opt.value) &&
                    payDateRuleType === opt.value && (
                      <>
                        <Select
                          value={payDateParticularDate}
                          onValueChange={setPayDateParticularDate}
                        >
                          <SelectTrigger className="ml-2 w-20">
                            <SelectValue placeholder="Date" />
                          </SelectTrigger>
                          <SelectContent>
                            {PERIOD_DATES.map((d) => (
                              <SelectItem key={d} value={d}>
                                {d}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <Select
                          value={payDateWhichMonth}
                          onValueChange={setPayDateWhichMonth}
                        >
                          <SelectTrigger className="ml-2 w-28">
                            <SelectValue placeholder="Month" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="this">This Month</SelectItem>
                            <SelectItem value="next">Next Month</SelectItem>
                          </SelectContent>
                        </Select>
                      </>
                    )}
                </div>
              ))}
            </RadioGroup>
          </div>
          {safePayFrequency &&
            ((["weekly", "two_weekly", "four_weekly"].includes(
              safePayFrequency,
            ) &&
              periodEndDay &&
              payDateRuleType) ||
              (["monthly", "quarterly", "yearly"].includes(safePayFrequency) &&
                periodEndDate &&
                payDateRuleType)) && (
              <div className="mt-4">
                <label className="mb-1 block font-semibold">
                  Schedule Preview
                </label>
                <div className="overflow-x-auto">
                  <table className="min-w-full border border-gray-300 text-sm">
                    <thead>
                      <tr className="bg-gray-100">
                        <th className="border px-2 py-1">#</th>
                        <th className="border px-2 py-1">Start Date</th>
                        <th className="border px-2 py-1">End Date</th>
                        <th className="border px-2 py-1">Pay Date</th>
                        <th className="border px-2 py-1">Tax Period</th>
                      </tr>
                    </thead>
                    <tbody>
                      {generatePayPeriodsForTaxYear({
                        frequency: safePayFrequency,
                        periodEndDay,
                        periodEndDate,
                        payDateRule: buildPayDateRule(),
                        taxYear,
                      }).map((p, i) => (
                        <tr key={i} className="even:bg-gray-50">
                          <td className="border px-2 py-1">{i + 1}</td>
                          <td className="border px-2 py-1">
                            {formatDateToDDMMYYYY(p.period_start)}
                          </td>
                          <td className="border px-2 py-1">
                            {formatDateToDDMMYYYY(p.period_end)}
                          </td>
                          <td className="border px-2 py-1">
                            {formatDateToDDMMYYYY(p.pay_date)}
                          </td>
                          <td className="border px-2 py-1">
                            {p.tax_period_label}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          {error && <div className="text-sm text-red-600">{error}</div>}
          <DialogFooter>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting
                ? "Creating..."
                : `Create ${payFrequency.replace("_", " ")} Schedule`}
            </Button>
            <Button
              type="button"
              variant="secondary"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
