"use client";
console.log("[LAYOUT] layout.tsx loaded");
import { Lato, Open_Sans } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/providers/theme-provider";
import { TaxYearProvider } from "@/providers/tax-year-provider";
import { EmployerDBProvider } from "@/providers/employer-db-provider";
import { MainNavbar } from "@/components/layouts/main-navbar";
import { ActionToolbar } from "@/components/layouts/action-toolbar";
import {
  QueryClient,
  QueryClientProvider,
  QueryCache,
  MutationCache,
} from "@tanstack/react-query";
import { useState } from "react";

// Load Lato font (primary)
const lato = Lato({
  subsets: ["latin"],
  weight: ["300", "400", "700", "900"],
  variable: "--font-lato",
});

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Create the client only once per app instance with optimized settings
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // Data remains fresh for 5 minutes to reduce unnecessary refetches
            staleTime: 5 * 60 * 1000,
            // Keep unused data cached for 30 minutes
            gcTime: 30 * 60 * 1000,
            // Retry 3 times with exponential backoff
            retry: 3,
            retryDelay: (attemptIndex) =>
              Math.min(1000 * 2 ** attemptIndex, 30000),
            // Don't refetch on window focus to prevent flashing
            refetchOnWindowFocus: false,
            // Keep previous data while fetching new data to prevent flashing
            placeholderData: (previousData) => previousData,
          },
        },
        queryCache: new QueryCache({
          onError: (error, query) => {
            // Log errors but don't display them if data exists
            console.error(`Query error: ${error.message}`, query);
          },
        }),
        mutationCache: new MutationCache({
          onError: (error) => {
            console.error(`Mutation error: ${error.message}`);
          },
        }),
      }),
  );

  return (
    <html lang="en" suppressHydrationWarning className={`${lato.variable}`}>
      <QueryClientProvider client={queryClient}>
        <TaxYearProvider>
          <EmployerDBProvider>
            <ThemeProvider defaultTheme="light">
              <body className="bg-background flex h-screen min-h-0 flex-col overflow-hidden px-2 font-sans subpixel-antialiased">
                <MainNavbar />
                <ActionToolbar />
                <main className="flex min-h-0 flex-1 flex-col">{children}</main>
              </body>
            </ThemeProvider>
          </EmployerDBProvider>
        </TaxYearProvider>
      </QueryClientProvider>
    </html>
  );
}
