import { useState, useEffect, useRef } from 'react';

interface UseLoadingStateOptions {
  /**
   * Minimum time to show loading state (in ms) to prevent flashing
   */
  minLoadingTime?: number;
  /**
   * Delay before showing loading state (in ms) to prevent flashing for fast operations
   */
  loadingDelay?: number;
}

/**
 * Hook to manage loading states with anti-flashing behavior
 * 
 * This hook helps prevent UI flashing by:
 * 1. Delaying the loading state for fast operations
 * 2. Ensuring minimum loading time to prevent quick flashes
 * 3. Providing smooth transitions between states
 */
export function useLoadingState(
  isLoading: boolean,
  options: UseLoadingStateOptions = {}
) {
  const {
    minLoadingTime = 300,
    loadingDelay = 100,
  } = options;

  const [showLoading, setShowLoading] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const loadingStartTime = useRef<number | null>(null);
  const loadingDelayTimeout = useRef<NodeJS.Timeout | null>(null);
  const minLoadingTimeout = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (isLoading) {
      // Start loading - but delay showing the loading state
      setIsTransitioning(true);
      loadingDelayTimeout.current = setTimeout(() => {
        setShowLoading(true);
        loadingStartTime.current = Date.now();
      }, loadingDelay);
    } else {
      // Stop loading
      if (loadingDelayTimeout.current) {
        clearTimeout(loadingDelayTimeout.current);
        loadingDelayTimeout.current = null;
      }

      if (showLoading && loadingStartTime.current) {
        // Ensure minimum loading time has passed
        const elapsed = Date.now() - loadingStartTime.current;
        const remaining = Math.max(0, minLoadingTime - elapsed);

        if (remaining > 0) {
          minLoadingTimeout.current = setTimeout(() => {
            setShowLoading(false);
            setIsTransitioning(false);
            loadingStartTime.current = null;
          }, remaining);
        } else {
          setShowLoading(false);
          setIsTransitioning(false);
          loadingStartTime.current = null;
        }
      } else {
        setShowLoading(false);
        setIsTransitioning(false);
        loadingStartTime.current = null;
      }
    }

    // Cleanup function
    return () => {
      if (loadingDelayTimeout.current) {
        clearTimeout(loadingDelayTimeout.current);
      }
      if (minLoadingTimeout.current) {
        clearTimeout(minLoadingTimeout.current);
      }
    };
  }, [isLoading, loadingDelay, minLoadingTime, showLoading]);

  return {
    /**
     * Whether to show the loading state
     */
    showLoading,
    /**
     * Whether the component is transitioning between states
     */
    isTransitioning,
    /**
     * Whether the component should show content (not loading and not transitioning)
     */
    showContent: !showLoading && !isTransitioning,
  };
}

/**
 * Hook for managing data transitions with previous data preservation
 * 
 * This hook helps maintain smooth transitions when switching between data sets
 * by keeping the previous data visible until new data is fully loaded.
 */
export function useDataTransition<T>(
  data: T | undefined,
  isLoading: boolean,
  options: UseLoadingStateOptions = {}
) {
  const [displayData, setDisplayData] = useState<T | undefined>(data);
  const [hasData, setHasData] = useState(false);
  const { showLoading, showContent } = useLoadingState(isLoading, options);

  useEffect(() => {
    if (data !== undefined && !isLoading) {
      setDisplayData(data);
      setHasData(true);
    }
  }, [data, isLoading]);

  return {
    /**
     * Data to display (either current or previous data during transitions)
     */
    displayData,
    /**
     * Whether to show loading state
     */
    showLoading: showLoading && !hasData,
    /**
     * Whether to show content
     */
    showContent: showContent || hasData,
    /**
     * Whether we have any data to show
     */
    hasData,
  };
}

/**
 * Hook for managing multiple loading states
 * 
 * Useful when a component depends on multiple async operations
 */
export function useMultipleLoadingStates(
  loadingStates: boolean[],
  options: UseLoadingStateOptions = {}
) {
  const isAnyLoading = loadingStates.some(state => state);
  const areAllLoading = loadingStates.every(state => state);
  
  const anyLoading = useLoadingState(isAnyLoading, options);
  const allLoading = useLoadingState(areAllLoading, options);

  return {
    /**
     * Loading state when any of the operations is loading
     */
    anyLoading,
    /**
     * Loading state when all operations are loading
     */
    allLoading,
    /**
     * Whether any operation is loading
     */
    isAnyLoading,
    /**
     * Whether all operations are loading
     */
    areAllLoading,
  };
}
