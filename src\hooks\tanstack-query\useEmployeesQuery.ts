import { useQuery, useQueryClient, useMutation } from "@tanstack/react-query";
import { useEmployerDBContext } from "@/providers/employer-db-provider";
import { getEmployees, updateEmployee } from "@/services/employerDbService";
import type { Employee } from "@/lib/schemas/employee";

/**
 * TanStack Query hook for fetching employees from the active employer DB.
 * Returns { data, isLoading, error }.
 *
 * - Uses the active employer's dbPath from context
 * - Handles multi-employer support by using correct dbPath
 * - Returns an empty array if no employer is active
 */
export function useEmployeesQuery() {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;

  return useQuery<Employee[], Error>({
    queryKey: ["employees", dbPath],
    queryFn: async () => {
      if (!dbPath) return [];
      return await getEmployees(dbPath);
    },
    enabled: !!dbPath,
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 30, // 30 minutes cache time
    refetchOnWindowFocus: false,
    // Keep previous data while fetching new data to prevent flashing
    placeholderData: (previousData) => previousData,
  });
}

/**
 * Mutation hook to update an employee record.
 * Invalidates ['employees', dbPath] on success.
 */
export function useUpdateEmployeeMutation() {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  const queryClient = useQueryClient();
  return useMutation<Employee, Error, Employee>({
    mutationFn: (emp: Employee) => updateEmployee(dbPath!, emp),
    onSuccess: () => {
      if (dbPath)
        queryClient.invalidateQueries({ queryKey: ["employees", dbPath] });
    },
  });
}
