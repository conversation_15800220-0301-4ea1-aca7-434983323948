import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useEmployerDBContext } from "@/providers/employer-db-provider";
import { addEmployee, updateEmployee } from "@/services/employerDbService";
import type { Employee } from "@/lib/schemas/employee";

/**
 * TanStack Query mutation hook for adding or updating an employee in the employer DB.
 * Decides between add/update by presence of employee.id (new = no id).
 * Invalidates the employees query on success.
 */
export function useEmployeesMutation() {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  const queryClient = useQueryClient();

  return useMutation<Employee, Error, Employee>({
    mutationFn: async (employee) => {
      if (!dbPath) throw new Error("No active employer DB");
      if (!employee.id || employee.id === "") {
        return await addEmployee(db<PERSON><PERSON>, employee);
      } else {
        return await updateEmployee(db<PERSON><PERSON>, employee);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["employees", dbPath] });
    },
  });
}
