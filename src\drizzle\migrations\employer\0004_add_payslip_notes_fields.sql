-- Add show_on_payslip and is_repeating fields to payslip_notes table
-- This migration is safe to run multiple times

-- Check if the table already has the new columns by trying to select them
-- If this succeeds, the columns already exist and we don't need to do anything

-- First, let's check if we need to migrate by creating a test query
-- We'll use a more robust approach that works even if columns already exist

-- Create the new table structure (this will be our target)
CREATE TABLE IF NOT EXISTS payslip_notes_new (
    id TEXT PRIMARY KEY NOT NULL,
    payslip_id TEXT NOT NULL,
    content TEXT NOT NULL,
    show_on_payslip INTEGER DEFAULT 1 NOT NULL,
    is_repeating INTEGER DEFAULT 0 NOT NULL,
    created_at INTEGER DEFAULT (strftime('%s','now')) NOT NULL,
    updated_at INTEGER DEFAULT (strftime('%s','now')) NOT NULL,
    FOREIGN KEY (payslip_id) REFERENCES payslips(id) ON UPDATE no action ON DELETE no action
);

-- Copy data from old table to new table, handling both old and new schema
-- This INSERT will work whether the old table has the new columns or not
INSERT OR IGNORE INTO payslip_notes_new (id, payslip_id, content, show_on_payslip, is_repeating, created_at, updated_at)
SELECT
    id,
    payslip_id,
    content,
    COALESCE(
        CASE
            WHEN typeof(show_on_payslip) = 'null' THEN 1
            ELSE show_on_payslip
        END, 1
    ) as show_on_payslip,
    COALESCE(
        CASE
            WHEN typeof(is_repeating) = 'null' THEN 0
            ELSE is_repeating
        END, 0
    ) as is_repeating,
    created_at,
    updated_at
FROM payslip_notes;

-- Drop the old table and rename the new one
DROP TABLE payslip_notes;
ALTER TABLE payslip_notes_new RENAME TO payslip_notes;

-- Create the index
CREATE INDEX IF NOT EXISTS idx_payslip_notes_payslip ON payslip_notes (payslip_id);
