"use client";

import { EMPLOYER_DB_EXTENSION } from "@/constants/file";
import { useEmployerDBContext } from "@/providers/employer-db-provider";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

import { useState, useEffect, useRef, useMemo } from "react";
import { Button } from "@/components/ui/button";
import {
  Search,
  ChevronUp,
  ChevronDown,
  PlusCircle,
  FolderOpen,
  Trash2,
  CheckCircle2,
  AlertCircle,
  RefreshCw,
  FileEdit,
  ExternalLink,
} from "lucide-react";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuTrigger,
} from "@/components/ui/context-menu";
import {
  useAddExistingEmployer,
  useRemoveEmployer,
} from "@/hooks/tanstack-query/useEmployerMutations";

import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import EmployerCreateModal from "@/components/dashboard/create-employer/EmployerCreateModal";

// Define employer interface for type safety
interface Employer {
  id: string; // Changed from number to string to match the UUID format in the database
  name: string;
  paye_ref: string;
  accounts_ref: string;
  next_pay_date: string;
  employees: number;
  tax_code_notices: number;
  rti_submissions: number;
  notifications: number;
  filePath: string; // Path to employer DB file
}

// Define column type for sorting functionality
type ColumnKey = keyof Employer;

// Define column interface with additional properties
interface Column {
  key: ColumnKey;
  label: string;
}

import { useEmployersList } from "@/hooks/tanstack-query/useEmployersList";

// Define column configuration for table headers
const initialColumns: Column[] = [
  { key: "name", label: "Employer Name" },
  { key: "paye_ref", label: "PAYE Reference" },
  { key: "accounts_ref", label: "Accounts Office Ref" },
  { key: "next_pay_date", label: "Next Pay Run" },
  { key: "employees", label: "Employees" },
  { key: "tax_code_notices", label: "Tax Code Notices" },
  { key: "rti_submissions", label: "RTI Submissions" },
  { key: "notifications", label: "Notifications" },
];

export function EmployersTab() {
  const {
    openEmployer,
    openEmployerError,
    clearOpenEmployerError,
    openEmployers,
    closeEmployer,
  } = useEmployerDBContext();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showRemoveDialog, setShowRemoveDialog] = useState(false);
  const [employerToRemove, setEmployerToRemove] = useState<Employer | null>(
    null,
  );

  // Dialog states for success and error messages
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [successTitle, setSuccessTitle] = useState("Success");
  const [errorTitle, setErrorTitle] = useState("Error");

  // Dialog states for notifications
  const [showRemovedEmployersDialog, setShowRemovedEmployersDialog] =
    useState(false);
  const [showUpdatedEmployersDialog, setShowUpdatedEmployersDialog] =
    useState(false);

  // State to track which row has an open context menu
  const [activeRowId, setActiveRowId] = useState<string | null>(null);

  // TODO: Replace with actual app setting or logic for default employer directory
  const defaultFilePath = `./employers/NEW_EMPLOYER${EMPLOYER_DB_EXTENSION}`;

  const addExistingEmployerMutation = useAddExistingEmployer();
  const removeEmployerMutation = useRemoveEmployer();

  const handleOpenModal = () => setShowCreateModal(true);
  const handleCloseModal = () => setShowCreateModal(false);
  const handleSaveEmployer = (data: any) => {
    console.log("Employer to save:", data);
    setShowCreateModal(false);

    // Show success message
    setSuccessTitle("Employer Created");
    setSuccessMessage(
      `${data.name} has been created and added to your employers list.`,
    );
    setShowSuccessDialog(true);

    // Refresh the employers list
    refetch();
  };

  // Handle adding an existing employer
  const handleAddExistingEmployer = async () => {
    try {
      const result = await addExistingEmployerMutation.mutateAsync(""); // Empty string triggers file picker
      if (result.success) {
        if (result.multipleFiles) {
          // Multiple files were processed
          setSuccessTitle("Employers Added");
          const message = [
            `Successfully added ${result.totalAdded} of ${result.totalFiles} employer files.`,
          ];

          // Add details about successful imports
          if (result.results && result.results.length > 0) {
            message.push("");
            message.push("Added employers:");
            result.results.forEach((emp) => {
              message.push(`• ${emp.name}`);
            });
          }

          // Add details about errors if any
          if (result.errors && result.errors.length > 0) {
            message.push("");
            message.push("Failed to add:");
            result.errors.forEach((err) => {
              message.push(`• ${err.path}: ${err.error}`);
            });
          }

          setSuccessMessage(message.join("\n"));
          setShowSuccessDialog(true);
        } else {
          // Single file was processed
          setSuccessTitle("Employer Added");
          setSuccessMessage(
            `${result.employer?.name} has been added to your employers list.`,
          );
          setShowSuccessDialog(true);
        }
      } else {
        setErrorTitle("Error Adding Employer");
        setErrorMessage(result.error || "Failed to add employer.");
        setShowErrorDialog(true);
      }
    } catch (error) {
      setErrorTitle("Unexpected Error");
      setErrorMessage(
        `An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`,
      );
      setShowErrorDialog(true);
    }
  };

  // Handle removing an employer
  const handleRemoveEmployer = (employer: Employer) => {
    setEmployerToRemove(employer);
    setShowRemoveDialog(true);
  };

  // Confirm removal of an employer
  const confirmRemoveEmployer = async () => {
    if (!employerToRemove) return;

    try {
      const result = await removeEmployerMutation.mutateAsync(
        employerToRemove.id,
      );
      if (result.success) {
        setSuccessTitle("Employer Removed");
        setSuccessMessage(
          `${employerToRemove.name} has been removed from your employers list.`,
        );
        setShowSuccessDialog(true);
      } else {
        setErrorTitle("Error Removing Employer");
        setErrorMessage(result.error || "Failed to remove employer.");
        setShowErrorDialog(true);
      }
    } catch (error) {
      setErrorTitle("Unexpected Error");
      setErrorMessage(
        `An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`,
      );
      setShowErrorDialog(true);
    } finally {
      setShowRemoveDialog(false);
      setEmployerToRemove(null);
    }
  };

  // Use TanStack Query to fetch employers from master DB
  const {
    data: allEmployers = [],
    isLoading,
    isError,
    refetch,
    removedEmployers,
    updatedEmployers,
    clearRemovedEmployers,
    clearUpdatedEmployers,
  } = useEmployersList();
  const [sortColumn, setSortColumn] = useState<ColumnKey>("name");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const columns = useMemo(() => initialColumns, []);

  // Handle employer DB open errors (missing or invalid file)
  useEffect(() => {
    if (
      openEmployerError &&
      (openEmployerError.includes("Invalid or missing dbPath") ||
        openEmployerError.includes("not found") ||
        openEmployerError.toLowerCase().includes("no such file"))
    ) {
      // Refresh the employer list (same as pressing the refresh button)
      // Find the missing employer in openEmployers by dbPath
      const missingEmployer = openEmployers.find(
        (emp) =>
          openEmployerError.includes(emp.dbPath) ||
          openEmployerError.toLowerCase().includes(emp.dbPath.toLowerCase()),
      );
      if (missingEmployer) {
        closeEmployer(missingEmployer.id);
        // If this was the last open employer, clear activeEmployerId
        if (openEmployers.length === 1) {
          try {
            import("@/store/navigation-store").then(
              ({ useNavigationStore }) => {
                if (useNavigationStore) {
                  useNavigationStore.getState().setActiveEmployer(null);
                }
              },
            );
          } catch {}
        }
      }
      refetch();
      clearOpenEmployerError();
    }
  }, [
    openEmployerError,
    refetch,
    clearOpenEmployerError,
    openEmployers,
    closeEmployer,
  ]);

  // Show notification when employers are removed due to missing files
  useEffect(() => {
    if (removedEmployers && removedEmployers.length > 0) {
      setShowRemovedEmployersDialog(true);
    }
  }, [removedEmployers]);

  // Show notification when employers are updated due to file name changes
  useEffect(() => {
    if (updatedEmployers && updatedEmployers.length > 0) {
      setShowUpdatedEmployersDialog(true);
    }
  }, [updatedEmployers]);

  const displayedEmployers = useMemo(() => {
    let filteredData = [...allEmployers];

    // Apply search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filteredData = filteredData.filter(
        (employer: Employer) =>
          (employer.name ?? "").toLowerCase().includes(searchLower) ||
          (employer.paye_ref ?? "").toLowerCase().includes(searchLower) ||
          (employer.accounts_ref ?? "").toLowerCase().includes(searchLower),
      );
    }

    // Apply sorting (only by name for now)
    filteredData.sort((a: any, b: any) => {
      const aValue = a[sortColumn] ?? "";
      const bValue = b[sortColumn] ?? "";
      if (typeof aValue === "string" && typeof bValue === "string") {
        return sortDirection === "asc"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      } else {
        return sortDirection === "asc"
          ? Number(aValue) - Number(bValue)
          : Number(bValue) - Number(aValue);
      }
    });

    return filteredData;
  }, [allEmployers, searchTerm, sortColumn, sortDirection]);
  const tableRef = useRef<HTMLTableElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Handle search input changes
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Handle keyboard events for auto-focusing search
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Skip if user is already typing in an input or if it's a modifier key
      if (
        document.activeElement instanceof HTMLInputElement ||
        document.activeElement instanceof HTMLTextAreaElement ||
        e.ctrlKey ||
        e.altKey ||
        e.metaKey
      ) {
        return;
      }

      // Skip navigation keys and function keys
      const nonTypingKeys = [
        "Tab",
        "Escape",
        "Enter",
        "ArrowUp",
        "ArrowDown",
        "ArrowLeft",
        "ArrowRight",
        "Home",
        "End",
        "PageUp",
        "PageDown",
        "F1",
        "F2",
        "F3",
        "F4",
        "F5",
        "F6",
        "F7",
        "F8",
        "F9",
        "F10",
        "F11",
        "F12",
        " ", // Spacebar
      ];

      if (nonTypingKeys.includes(e.key)) {
        return;
      }

      // Focus search input and set its value to the pressed key if it's a printable character
      if (e.key.length === 1 && searchInputRef.current) {
        searchInputRef.current.focus();
        // Don't override the input's normal behavior
        // The key press will naturally be added to the input
      }
    };

    // Add event listener
    window.addEventListener("keydown", handleKeyDown);

    // Cleanup
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, []);

  // Handle column sorting
  const handleSort = (column: ColumnKey) => {
    if (sortColumn === column) {
      // Toggle direction if already sorting by this column
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      // Set new sort column and default to ascending
      setSortColumn(column);
      setSortDirection("asc");
    }
  };

  // Render sort direction icon
  const renderSortIcon = (column: ColumnKey) => {
    if (sortColumn !== column) return null;

    return sortDirection === "asc" ? (
      <ChevronUp className="ml-1 h-4 w-4" />
    ) : (
      <ChevronDown className="ml-1 h-4 w-4" />
    );
  };

  return (
    <div className="flex h-full flex-col">
      {/* Search and actions section */}
      <div className="flex items-center gap-3 border-b px-4 py-2">
        <div className="relative w-64">
          <Search className="text-muted-foreground absolute top-2.5 left-2 h-4 w-4" />
          <Input
            ref={searchInputRef}
            placeholder="Search employers..."
            value={searchTerm}
            onChange={handleSearch}
            className="pl-8"
            onKeyDown={(e) => {
              if (
                e.key === "Tab" &&
                !e.shiftKey &&
                displayedEmployers.length > 0 &&
                tableRef.current
              ) {
                e.preventDefault(); // Prevent default tab behavior
                // Find the first row and focus it
                const rows = tableRef.current.querySelectorAll("tbody tr");
                if (rows.length > 0) {
                  (rows[0] as HTMLElement).focus();
                }
              }
            }}
          />
        </div>

        <Button onClick={handleOpenModal}>
          <PlusCircle className="mr-2 h-4 w-4" />
          New Employer
        </Button>
        <Button
          variant="outline"
          onClick={handleAddExistingEmployer}
          disabled={addExistingEmployerMutation.isPending}
        >
          <FolderOpen className="mr-2 h-4 w-4" />
          Add Existing
        </Button>
        <Button
          variant="outline"
          size="icon"
          onClick={() => refetch()}
          disabled={isLoading}
          title="Refresh employers list"
        >
          {isLoading ? (
            <RefreshCw className="h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4" />
          )}
        </Button>
        <EmployerCreateModal
          isOpen={showCreateModal}
          onClose={handleCloseModal}
          onSave={handleSaveEmployer}
          defaultFilePath={defaultFilePath}
        />

        {/* Remove Employer Confirmation Dialog */}
        <AlertDialog open={showRemoveDialog} onOpenChange={setShowRemoveDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Remove Employer</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to remove {employerToRemove?.name} from
                your employers list?
                <br />
                <br />
                <strong>Note:</strong> This will only remove the employer from
                your list. The employer database file will not be deleted.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmRemoveEmployer}
                className="bg-red-600 hover:bg-red-700"
              >
                Remove
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Success Dialog */}
        <AlertDialog
          open={showSuccessDialog}
          onOpenChange={setShowSuccessDialog}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle className="flex items-center gap-2">
                <CheckCircle2 className="h-5 w-5 text-green-500" />
                {successTitle}
              </AlertDialogTitle>
              <AlertDialogDescription asChild>
                <div className="text-muted-foreground max-h-[70vh] overflow-y-auto pr-2 text-sm whitespace-pre-line">
                  {successMessage}
                </div>
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogAction onClick={() => setShowSuccessDialog(false)}>
                OK
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Error Dialog */}
        <AlertDialog open={showErrorDialog} onOpenChange={setShowErrorDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5 text-red-500" />
                {errorTitle}
              </AlertDialogTitle>
              <AlertDialogDescription>{errorMessage}</AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogAction onClick={() => setShowErrorDialog(false)}>
                OK
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Removed Employers Dialog */}
        <AlertDialog
          open={showRemovedEmployersDialog}
          onOpenChange={(open) => {
            setShowRemovedEmployersDialog(open);
            if (!open) clearRemovedEmployers();
          }}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5 text-amber-500" />
                Missing Employer Files Detected
              </AlertDialogTitle>
              <AlertDialogDescription asChild>
                <div className="text-muted-foreground max-h-[70vh] space-y-4 overflow-y-auto pr-2 text-sm">
                  <span>
                    The following employer files could not be found and have
                    been removed from your list:
                  </span>
                  <ul className="list-disc space-y-1 pl-5">
                    {removedEmployers?.map((employer) => (
                      <li key={employer.id}>
                        <span className="font-medium">{employer.name}</span>
                        <span className="ml-2 text-xs">
                          (File: {employer.filePath})
                        </span>
                      </li>
                    ))}
                  </ul>
                  <span className="mt-2 block">
                    These files may have been deleted, moved, or renamed outside
                    of the application.
                  </span>
                </div>
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogAction
                onClick={() => {
                  setShowRemovedEmployersDialog(false);
                  clearRemovedEmployers();
                }}
              >
                OK
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Updated Employers Dialog */}
        <AlertDialog
          open={showUpdatedEmployersDialog}
          onOpenChange={(open) => {
            setShowUpdatedEmployersDialog(open);
            if (!open) clearUpdatedEmployers();
          }}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle className="flex items-center gap-2">
                <CheckCircle2 className="h-5 w-5 text-blue-500" />
                Employer Names Updated
              </AlertDialogTitle>
              <AlertDialogDescription asChild>
                <div className="text-muted-foreground max-h-[70vh] space-y-4 overflow-y-auto pr-2 text-sm">
                  <span>
                    The following employer names have been updated to match
                    their file names:
                  </span>
                  <ul className="list-disc space-y-1 pl-5">
                    {updatedEmployers?.map((employer) => (
                      <li key={employer.id}>
                        <span className="font-medium">{employer.newName}</span>
                        <span className="ml-2 text-xs">
                          (Previously: {employer.oldName})
                        </span>
                      </li>
                    ))}
                  </ul>
                  <span className="mt-2 block">
                    This ensures that trading names in file names are displayed
                    in the employer list. The system also detects when files are
                    renamed in File Explorer and updates the references
                    automatically.
                  </span>
                </div>
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogAction
                onClick={() => {
                  setShowUpdatedEmployersDialog(false);
                  clearUpdatedEmployers();
                }}
              >
                OK
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        <div className="text-muted-foreground text-sm">
          {isLoading
            ? "Loading..."
            : isError
              ? "Error loading employers"
              : `${allEmployers.length} employers`}
        </div>
      </div>

      {/* Table container */}
      <div className="bg-background flex min-h-0 flex-grow flex-col rounded-lg shadow-sm">
        <div className="min-h-0 flex-grow overflow-y-auto">
          <Table ref={tableRef} className="min-w-full">
            <TableHeader className="bg-background sticky top-0 z-10 h-14">
              <TableRow>
                {columns.map((column) => (
                  <TableHead
                    key={column.key}
                    className={`relative font-normal text-emerald-600 select-none dark:text-emerald-400 ${
                      [
                        "employees",
                        "tax_code_notices",
                        "rti_submissions",
                        "notifications",
                      ].includes(column.key)
                        ? "text-center"
                        : ""
                    }`}
                  >
                    <div
                      className="hover:bg-muted-foreground/10 hover:border-border dark:hover:bg-muted dark:hover:border-muted-foreground flex h-full cursor-pointer items-center p-2 hover:border"
                      onClick={() => handleSort(column.key)}
                    >
                      <span className="flex-grow truncate">{column.label}</span>
                      {renderSortIcon(column.key)}
                    </div>
                  </TableHead>
                ))}
                {/* No actions column needed with context menu */}
              </TableRow>
            </TableHeader>
            <TableBody>
              {displayedEmployers.map((employer: Employer) => (
                <ContextMenu
                  key={employer.id}
                  onOpenChange={(open) => {
                    if (open) {
                      setActiveRowId(employer.id);
                    } else {
                      setActiveRowId(null);
                    }
                  }}
                >
                  <ContextMenuTrigger asChild>
                    <TableRow
                      tabIndex={0}
                      className={`transition-colors duration-50 even:bg-slate-100 hover:bg-slate-200 dark:even:bg-black/20 dark:hover:bg-slate-800/30 ${activeRowId === employer.id ? "!border-l-4 !border-l-blue-500 bg-blue-100 dark:bg-blue-900/30" : ""}`}
                      onClick={() =>
                        openEmployer({
                          id: employer.id,
                          name: employer.name,
                          dbPath: employer.filePath,
                        })
                      }
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          openEmployer({
                            id: employer.id,
                            name: employer.name,
                            dbPath: employer.filePath,
                          });
                        }
                      }}
                    >
                      {columns.map((column) => (
                        <TableCell
                          key={`${employer.id}-${column.key}`}
                          className={
                            column.key === "name"
                              ? "font-normal text-slate-900 dark:text-white"
                              : [
                                    "employees",
                                    "tax_code_notices",
                                    "rti_submissions",
                                    "notifications",
                                  ].includes(column.key)
                                ? "text-center"
                                : ""
                          }
                        >
                          {column.key === "tax_code_notices" ? (
                            (employer.tax_code_notices ?? 0 > 0) ? (
                              <Badge
                                variant="secondary"
                                className="bg-amber-100 text-amber-800 hover:bg-amber-100 dark:bg-amber-900/30 dark:text-amber-300 dark:hover:bg-amber-900/50"
                              >
                                {employer.tax_code_notices ?? 0}
                              </Badge>
                            ) : (
                              0
                            )
                          ) : column.key === "rti_submissions" ? (
                            (employer.rti_submissions ?? 0 > 0) ? (
                              <Badge
                                variant="secondary"
                                className="bg-sky-100 text-sky-800 hover:bg-sky-100 dark:bg-sky-900/30 dark:text-sky-300 dark:hover:bg-sky-900/50"
                              >
                                {employer.rti_submissions ?? 0}
                              </Badge>
                            ) : (
                              0
                            )
                          ) : column.key === "notifications" ? (
                            (employer.notifications ?? 0 > 0) ? (
                              <Badge
                                variant="secondary"
                                className="bg-red-100 text-red-800 hover:bg-red-100 dark:bg-red-900/30 dark:text-red-300 dark:hover:bg-red-900/50"
                              >
                                {employer.notifications ?? 0}
                              </Badge>
                            ) : (
                              0
                            )
                          ) : (
                            (employer[column.key] ?? "")
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  </ContextMenuTrigger>
                  <ContextMenuContent>
                    <ContextMenuItem
                      onClick={() =>
                        openEmployer({
                          id: employer.id,
                          name: employer.name,
                          dbPath: employer.filePath,
                        })
                      }
                    >
                      <ExternalLink className="mr-2 h-4 w-4" />
                      Open Employer
                    </ContextMenuItem>
                    <ContextMenuItem
                      onClick={() => {
                        // Placeholder for future employer editing functionality
                        console.log(`Editing employer: ${employer.name}`);
                      }}
                    >
                      <FileEdit className="mr-2 h-4 w-4" />
                      Edit Details
                    </ContextMenuItem>
                    <ContextMenuSeparator />
                    <ContextMenuItem
                      variant="destructive"
                      onClick={() => handleRemoveEmployer(employer)}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Remove Employer
                    </ContextMenuItem>
                  </ContextMenuContent>
                </ContextMenu>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
      <span className="h-10" />
    </div>
  );
}
