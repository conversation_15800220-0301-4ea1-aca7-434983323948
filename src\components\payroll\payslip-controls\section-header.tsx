"use client";

import React, { ReactNode } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { AddElementButton } from "./add-element-button";
import { Trash2, RotateCcw } from "lucide-react";
import {
  Toolt<PERSON>,
  TooltipContent,
  Toolt<PERSON>Provider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { usePayslipReadOnly } from "@/providers/payslip-readonly-provider";

type SectionType = "basic" | "additions" | "deductions";

interface SectionHeaderProps {
  title: string;
  sectionType?: SectionType;
  addButtons?: Array<{
    label: string;
    onClick: () => void;
  }>;
  actionButtons?: Array<{
    label: string;
    onClick: () => void;
    variant?: "default" | "outline" | "destructive";
    icon?: ReactNode;
  }>;
  className?: string;
  titleClassName?: string;
}

export const SectionHeader: React.FC<SectionHeaderProps> = ({
  title,
  sectionType = "basic",
  addButtons = [],
  actionButtons = [],
  className = "",
  titleClassName = "text-base font-normal text-slate-800 dark:text-slate-200",
}) => {
  // Get read-only state from context
  const { isReadOnly, getDisabledClassName } = usePayslipReadOnly();

  // Find the clear and delete actions if they exist
  const clearAction = actionButtons.find((btn) => btn.label === "Clear Values");
  const deleteAction = actionButtons.find((btn) => btn.label === "Delete All");

  return (
    <div className={`mb-2 flex items-center justify-between ${className}`}>
      {/* Left column - Heading with action icons */}
      <div className="flex items-center gap-2">
        <h3 className={titleClassName}>{title}</h3>

        {/* Small action icons */}
        <div className="flex items-center gap-1">
          {clearAction && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className={getDisabledClassName("h-5 w-5")}
                  onClick={clearAction.onClick}
                  disabled={isReadOnly}
                >
                  <RotateCcw className="h-3.5 w-3.5 text-slate-400 hover:text-slate-600 dark:text-slate-300" />
                </Button>
              </TooltipTrigger>
              <TooltipContent
                side="top"
                align="center"
                className="border-zinc-700 bg-zinc-800 text-white"
              >
                <p>Clear Values</p>
              </TooltipContent>
            </Tooltip>
          )}

          {deleteAction && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className={getDisabledClassName("h-5 w-5")}
                  onClick={deleteAction.onClick}
                  disabled={isReadOnly}
                >
                  <Trash2 className="text-muted-foreground h-3.5 w-3.5" />
                </Button>
              </TooltipTrigger>
              <TooltipContent
                side="top"
                align="center"
                className="border-zinc-700 bg-zinc-800 text-white"
              >
                <p>Delete All</p>
              </TooltipContent>
            </Tooltip>
          )}
        </div>
      </div>

      {/* Right column - Add buttons */}
      <div className={getDisabledClassName("ml-15 flex gap-2")}>
        {addButtons.map((button, index) => (
          <AddElementButton
            key={`add-button-${index}`}
            label={button.label}
            onClick={button.onClick}
            sectionType={sectionType}
            disabled={isReadOnly}
          />
        ))}
      </div>
    </div>
  );
};
