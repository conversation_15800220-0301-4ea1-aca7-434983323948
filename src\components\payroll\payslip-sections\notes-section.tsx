"use client";

import React, { useState, useEffect, useRef, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Trash2 } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { SectionHeader } from "@/components/payroll/payslip-controls";
import { RepeatingControl } from "@/components/payroll/payslip-controls/repeating-control";
import { ShowOnPayslipControl } from "@/components/payroll/payslip-controls/show-on-payslip-control";
import { usePayslipReadOnly } from "@/providers/payslip-readonly-provider";
import { useEmployerDBContext } from "@/providers/employer-db-provider";
import {
  usePayslip,
  useUpsertPayslipNoteMutation,
  useDeletePayslipNoteMutation,
  useCreatePayslipMutation,
} from "@/hooks/tanstack-query/usePayslip";
import { useQueryClient } from "@tanstack/react-query";
import type { PayslipNote } from "@/drizzle/schema/employer/payslip";

interface NoteItem {
  id: string;
  content: string;
  showOnPayslip: boolean;
  isRepeating: boolean;
}

interface PayslipNotesSectionProps {
  employeeId: string;
  periodId: string;
}

const PayslipNotesSection: React.FC<PayslipNotesSectionProps> = ({
  employeeId,
  periodId,
}) => {
  // Get read-only state from context
  const { isReadOnly, getDisabledClassName } = usePayslipReadOnly();

  const initRef = useRef(false);
  const notesRef = useRef<NoteItem[]>([]);
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;

  const { data, isLoading } = usePayslip(employeeId, periodId);
  const upsert = useUpsertPayslipNoteMutation(employeeId, periodId);
  const del = useDeletePayslipNoteMutation(employeeId, periodId);
  const createPayslip = useCreatePayslipMutation(employeeId, periodId);
  const queryClient = useQueryClient();

  const [notes, setNotes] = useState<NoteItem[]>([]);
  const isBusy = isLoading || !data;

  // Reset and initialize notes from fetched data once per payslip
  useEffect(() => {
    setNotes([]);
    initRef.current = false;
  }, [employeeId, periodId]);

  useEffect(() => {
    if (data) {
      // Transform database notes to UI format
      const transformedNotes = data.notes.map((note: PayslipNote) => ({
        id: note.id,
        content: note.content,
        showOnPayslip: note.show_on_payslip ?? true,
        isRepeating: note.is_repeating ?? false,
      }));

      setNotes([...transformedNotes]);
      notesRef.current = [...transformedNotes];
      initRef.current = true;
    } else if (data && data.notes?.length === 0) {
      setNotes([]);
      notesRef.current = [];
      initRef.current = true;
    }
  }, [data, employeeId, periodId]);

  // Save changes to database with debouncing
  const saveChanges = useCallback(() => {
    const currentNotes = notesRef.current;
    const payId = data?.payslip?.id;

    const upsertAll = (payslipId: string, notesToSave: NoteItem[]) => {
      notesToSave.forEach((note) => {
        const noteTempId = note.id;
        const payload: any = {
          payslip_id: payslipId,
          content: note.content,
          show_on_payslip: note.showOnPayslip,
          is_repeating: note.isRepeating,
        };

        // Use temp id prefix to detect new notes vs existing DB items
        const isTemp = note.id.startsWith("note-");
        if (!isTemp) {
          payload.id = note.id;
        }

        upsert.mutate(payload, {
          onSuccess: (savedNote) => {
            // On create, remap the UI note id to the real DB id
            if (!payload.id) {
              setNotes((current) =>
                current.map((n) =>
                  n.id === noteTempId ? { ...n, id: savedNote.id } : n,
                ),
              );
              notesRef.current = notesRef.current.map((n) =>
                n.id === noteTempId ? { ...n, id: savedNote.id } : n,
              );
            }
            if (dbPath)
              queryClient.invalidateQueries({
                queryKey: ["payslip", dbPath, employeeId, periodId],
              });
          },
        });
      });
    };

    if (!payId) {
      // Only create a payslip once: only invoke when idle
      if (createPayslip.status === "idle") {
        createPayslip.mutate(undefined, {
          onSuccess: (slip) => upsertAll(slip.id, currentNotes),
        });
      }
    } else {
      upsertAll(payId, currentNotes);
    }
  }, [
    data?.payslip?.id,
    createPayslip,
    upsert,
    dbPath,
    queryClient,
    employeeId,
    periodId,
  ]);

  // Auto-save when notes change (with debouncing)
  useEffect(() => {
    if (initRef.current && notes.length > 0) {
      const timeoutId = setTimeout(() => {
        saveChanges();
      }, 300);
      return () => clearTimeout(timeoutId);
    }
  }, [notes, saveChanges]);

  const handleChange = (newNotes: NoteItem[]) => {
    // Detect and delete removed notes
    const prevIds = notes.map((n) => n.id);
    setNotes(newNotes);
    notesRef.current = newNotes;

    const newIds = newNotes.map((n) => n.id);
    const removedIds = prevIds.filter((id) => !newIds.includes(id));
    removedIds.forEach((id) => {
      // Only delete if it's not a temp ID (i.e., it exists in database)
      if (!id.startsWith("note-")) {
        del.mutate(id, {
          onSuccess: () => {
            if (dbPath)
              queryClient.invalidateQueries({
                queryKey: ["payslip", dbPath, employeeId, periodId],
              });
          },
        });
      }
    });
  };

  // Handle input changes
  const handleInputChange = (
    id: string,
    field: keyof NoteItem,
    value: string | boolean,
  ) => {
    const newNotes = [...notes];
    const index = newNotes.findIndex((item) => item.id === id);

    if (index !== -1) {
      newNotes[index] = { ...newNotes[index], [field]: value };
      handleChange(newNotes);
    }
  };

  // Add a new note
  const handleAddNote = () => {
    const newNote: NoteItem = {
      id: `note-${Date.now()}`,
      content: "",
      showOnPayslip: true,
      isRepeating: false,
    };

    handleChange([...notes, newNote]);
  };

  // Remove a note
  const handleRemoveNote = (id: string) => {
    handleChange(notes.filter((item) => item.id !== id));
  };

  if (isBusy) {
    return (
      <div className="space-y-2">
        <SectionHeader
          title="Notes"
          addButtons={[{ label: "Add Note", onClick: () => {} }]}
        />
        <div className="text-muted-foreground p-2 text-center text-xs">
          Loading notes...
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <SectionHeader
        title="Notes"
        addButtons={[{ label: "Add Note", onClick: handleAddNote }]}
      />

      {notes.length === 0 ? (
        <div className="text-muted-foreground p-2 text-center text-xs">
          No notes added. Click &quot;Add Note&quot; to create a new note.
        </div>
      ) : (
        notes.map((note) => (
          <div key={note.id} className="bg-card mb-1 rounded-lg p-2">
            <div className="flex items-start justify-between">
              <div className="w-full space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Label
                      htmlFor={`note-${note.id}`}
                      className="text-xs font-medium"
                    >
                      Note
                    </Label>
                    <ShowOnPayslipControl
                      id={note.id}
                      isChecked={note.showOnPayslip}
                      onChange={(checked) =>
                        handleInputChange(note.id, "showOnPayslip", checked)
                      }
                    />
                    <RepeatingControl
                      id={note.id}
                      isChecked={note.isRepeating}
                      onChange={(checked) =>
                        handleInputChange(note.id, "isRepeating", checked)
                      }
                    />
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    className={getDisabledClassName("h-6 w-6")}
                    onClick={() => handleRemoveNote(note.id)}
                    disabled={isReadOnly}
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>

                <Textarea
                  id={`note-${note.id}`}
                  value={note.content}
                  onChange={(e) =>
                    handleInputChange(note.id, "content", e.target.value)
                  }
                  placeholder="Enter note text here..."
                  disabled={isReadOnly}
                  className={getDisabledClassName("min-h-[60px] text-xs")}
                />
              </div>
            </div>
          </div>
        ))
      )}
    </div>
  );
};

export default PayslipNotesSection;
