"use client";

import React from "react";
import { ClickableCheckbox } from "@/components/ui/clickable-checkbox";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { RotateCcw } from "lucide-react";
import { usePayslipReadOnly } from "@/providers/payslip-readonly-provider";

interface ZeroizeControlProps {
  id: string;
  isChecked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
  className?: string;
}

export const ZeroizeControl: React.FC<ZeroizeControlProps> = ({
  id,
  isChecked,
  onChange,
  disabled = false,
  className = "",
}) => {
  // Get read-only state from context
  const { isReadOnly, getDisabledClassName } = usePayslipReadOnly();

  // Combine external disabled prop with read-only state
  const isDisabled = disabled || isReadOnly;
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <div className={getDisabledClassName(className)}>
          <ClickableCheckbox
            id={`zeroize-${id}`}
            className="size-4 data-[state=checked]:border-amber-600 data-[state=checked]:bg-amber-400"
            checked={isChecked}
            onCheckedChange={(checked) => onChange(checked === true)}
            disabled={isDisabled}
            label={
              <RotateCcw
                className={`size-4 ${
                  isChecked && !isDisabled
                    ? "text-amber-500"
                    : "text-muted-foreground"
                }`}
              />
            }
            wrapperClassName="flex items-center"
          />
        </div>
      </TooltipTrigger>
      <TooltipContent>
        <p>Zeroize Next</p>
      </TooltipContent>
    </Tooltip>
  );
};
