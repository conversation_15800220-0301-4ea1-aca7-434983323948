// Service layer for all employer DB operations (renderer process)
// All functions use Electron IPC to call the main process

export async function getPayPeriods(dbPath: string) {
  console.log("[Service] getPayPeriods called with dbPath:", dbPath);
  const result = await window.api.invoke("employerDb:getPayPeriods", dbPath);
  console.log("[Service] getPayPeriods result:", result);
  if (result.success) return result.payPeriods;
  throw new Error(result.error || "Failed to fetch pay periods");
}

export async function insertSinglePayPeriod(dbPath: string, payPeriod: any) {
  const result = await window.api.invoke(
    "employerDb:addPayPeriod",
    dbPath,
    payPeriod,
  );
  if (result.success) return result.payPeriod;
  throw new Error(result.error || "Failed to add pay period");
}

// Add schedule and all pay periods for it (batch)
export async function insertPayPeriodScheduleAndPeriods(
  dbPath: string,
  payload: { label: string; type: string; tax_year: string; periods: any[] },
) {
  const result = await window.api.invoke(
    "employerDb:addPayPeriodScheduleAndPeriods",
    dbPath,
    payload,
  );
  if (result.success) return result;
  throw new Error(
    result.error || "Failed to add pay period schedule and periods",
  );
}

// Add multiple pay periods (batch)
export async function insertMultiplePayPeriods(
  dbPath: string,
  payPeriods: any[],
) {
  const result = await window.api.invoke(
    "employerDb:addPayPeriods",
    dbPath,
    payPeriods,
  );
  if (result.success) return result.payPeriods;
  throw new Error(result.error || "Failed to add pay periods");
}

export async function updatePayPeriod(dbPath: string, payPeriod: any) {
  const result = await window.api.invoke(
    "employerDb:updatePayPeriod",
    dbPath,
    payPeriod,
  );
  if (result.success) return result.payPeriod;
  throw new Error(result.error || "Failed to update pay period");
}

export async function deletePayPeriod(dbPath: string, id: string) {
  const result = await window.api.invoke(
    "employerDb:deletePayPeriod",
    dbPath,
    id,
  );
  if (result.success) return id;
  throw new Error(result.error || "Failed to delete pay period");
}

export async function openEmployerDb(
  dbPath: string,
): Promise<{ success: boolean; error?: string }> {
  // window.api.invoke is the standard IPC bridge (preload script must expose this)
  return await window.api.invoke("employerDb:openPersistent", dbPath);
}

// Example: get payrolls for an employer (add more as needed)
export async function getPayrolls(employerId: string): Promise<any[]> {
  return await window.api.invoke("employerDb:getPayrolls", employerId);
}

import type { Employee } from "@/lib/schemas/employee";
import type {
  Payslip,
  PayslipLineItem,
  PayslipItemType,
  PayslipNote,
  PayslipAdditionsLineItem,
  PayslipDeductionsLineItem,
} from "../drizzle/schema/employer/payslip";

export async function getEmployees(dbPath: string): Promise<Employee[]> {
  const result = await window.api.invoke("employerDb:getEmployees", dbPath);
  console.log("[getEmployees] IPC result:", result);
  if (result.success) {
    return result.employees;
  }
  throw new Error(result.error || "Failed to fetch employees");
}

// Add a new employee to the employer DB
export async function addEmployee(
  dbPath: string,
  employee: Employee,
): Promise<Employee> {
  // Assign a new UUID if id is missing or empty
  if (!employee.id || employee.id === "") {
    employee.id = crypto.randomUUID();
  }
  const result = await window.api.invoke(
    "employerDb:addEmployee",
    dbPath,
    employee,
  );
  if (result.success) {
    return result.employee;
  }
  throw new Error(result.error || "Failed to add employee");
}

// Update an existing employee in the employer DB
export async function updateEmployee(
  dbPath: string,
  employee: Employee,
): Promise<Employee> {
  const result = await window.api.invoke(
    "employerDb:updateEmployee",
    dbPath,
    employee,
  );
  if (result.success) {
    return result.employee;
  }
  throw new Error(result.error || "Failed to update employee");
}

// Delete all pay periods by type and scheduleId
export async function deletePayPeriodsByTypeAndName(
  dbPath: string,
  type: string,
  scheduleId: string | null,
) {
  const result = await window.api.invoke(
    "employerDb:deletePayPeriodsByTypeAndName",
    dbPath,
    type,
    scheduleId,
  );
  if (result.success) return result.deletedCount;
  throw new Error(result.error || "Failed to delete pay periods for schedule");
}

// Create a new payslip record
export async function createPayslip(
  dbPath: string,
  employeeId: string,
  periodId: string,
): Promise<Payslip> {
  const result = await window.api.invoke(
    "employerDb:createPayslip",
    dbPath,
    employeeId,
    periodId,
  );
  if (result.success) return result.payslip;
  throw new Error(result.error || "Failed to create payslip");
}

// Fetch unique payment frequencies from the employee table in the employer DB.
export async function getUniquePayFrequencies(
  dbPath: string,
): Promise<string[]> {
  const result = await window.api.invoke(
    "employerDb:getUniquePayFrequencies",
    dbPath,
  );
  if (result.success) {
    return result.frequencies;
  }
  throw new Error(result.error || "Failed to fetch unique pay frequencies");
}

/**
 * Fetch pay period schedules from the employer DB.
 * Returns an array of schedules, e.g. [{ label: "Schedule 1", type: "Monthly" }]
 */
export async function getPayPeriodSchedules(
  dbPath: string,
  taxYear: string,
): Promise<any[]> {
  const result = await window.api.invoke(
    "employerDb:getPayPeriodSchedules",
    dbPath,
    taxYear,
  );
  if (result.success) return result.schedules;
  throw new Error(result.error || "Failed to fetch pay period schedules");
}

// Payslip service functions
export async function getPayslip(
  dbPath: string,
  employeeId: string,
  periodId: string,
): Promise<{
  payslip: Payslip | null;
  items: PayslipLineItem[];
  notes: PayslipNote[];
  additions: PayslipAdditionsLineItem[];
  deductions: PayslipDeductionsLineItem[];
}> {
  const result = await window.api.invoke(
    "employerDb:getPayslip",
    dbPath,
    employeeId,
    periodId,
  );
  if (result.success)
    return {
      payslip: result.payslip,
      items: result.items,
      notes: result.notes,
      additions: result.additions,
      deductions: result.deductions,
    };
  throw new Error(result.error || "Failed to fetch payslip");
}

/**
 * Finalise selected payslips and open next period
 */
export async function finalisePayslips(
  dbPath: string,
  periodId: string,
  slipIds: string[],
): Promise<void> {
  const result = await window.api.invoke(
    "employerDb:finalisePayslips",
    dbPath,
    periodId,
    slipIds,
  );
  if (result.success) return;
  throw new Error(result.error || "Failed to finalise payslips");
}

/**
 * Reopen selected payslips
 */
export async function reopenPayslips(
  dbPath: string,
  periodId: string,
  employeeIds: string[],
): Promise<void> {
  const result = await window.api.invoke(
    "employerDb:reopenPayslips",
    dbPath,
    periodId,
    employeeIds,
  );
  if (result.success) return;
  throw new Error(result.error || "Failed to reopen payslips");
}

export async function getItemTypes(dbPath: string): Promise<PayslipItemType[]> {
  const result = await window.api.invoke("employerDb:getItemTypes", dbPath);
  if (result.success) return result.itemTypes;
  throw new Error(result.error || "Failed to fetch item types");
}

export async function upsertPayslipLineItem(
  dbPath: string,
  item: Partial<PayslipLineItem> & { payslip_id: string },
): Promise<PayslipLineItem> {
  const result = await window.api.invoke(
    "employerDb:upsertPayslipLineItem",
    dbPath,
    item,
  );
  if (result.success) return result.lineItem;
  throw new Error(result.error || "Failed to upsert line item");
}

export async function deletePayslipLineItem(
  dbPath: string,
  id: string,
): Promise<number> {
  const result = await window.api.invoke(
    "employerDb:deletePayslipLineItem",
    dbPath,
    id,
  );
  if (result.success) return result.deletedCount;
  throw new Error(result.error || "Failed to delete line item");
}

export async function upsertPayslipNote(
  dbPath: string,
  note: Partial<PayslipNote> & { payslip_id: string },
): Promise<PayslipNote> {
  const result = await window.api.invoke(
    "employerDb:upsertPayslipNote",
    dbPath,
    note,
  );
  if (result.success) return result.note;
  throw new Error(result.error || "Failed to upsert payslip note");
}

export async function addPayslipNote(
  dbPath: string,
  payslipId: string,
  content: string,
): Promise<PayslipNote> {
  const result = await window.api.invoke(
    "employerDb:addPayslipNote",
    dbPath,
    payslipId,
    content,
  );
  if (result.success) return result.note;
  throw new Error(result.error || "Failed to add payslip note");
}

export async function deletePayslipNote(
  dbPath: string,
  noteId: string,
): Promise<number> {
  const result = await window.api.invoke(
    "employerDb:deletePayslipNote",
    dbPath,
    noteId,
  );
  if (result.success) return result.deletedCount;
  throw new Error(result.error || "Failed to delete payslip note");
}

// Additions functions
export async function getAdditionsItemTypes(dbPath: string): Promise<any[]> {
  const result = await window.api.invoke(
    "employerDb:getAdditionsItemTypes",
    dbPath,
  );
  if (result.success) return result.itemTypes;
  throw new Error(result.error || "Failed to fetch additions item types");
}

export async function upsertPayslipAdditionsLineItem(
  dbPath: string,
  item: any,
): Promise<any> {
  const result = await window.api.invoke(
    "employerDb:upsertPayslipAdditionsLineItem",
    dbPath,
    item,
  );
  if (result.success) return result.lineItem;
  throw new Error(result.error || "Failed to upsert additions line item");
}

export async function deletePayslipAdditionsLineItem(
  dbPath: string,
  id: string,
): Promise<number> {
  const result = await window.api.invoke(
    "employerDb:deletePayslipAdditionsLineItem",
    dbPath,
    id,
  );
  if (result.success) return result.deletedCount;
  throw new Error(result.error || "Failed to delete additions line item");
}

export async function clearAllPayslipAdditionsLineItems(
  dbPath: string,
  payslipId: string,
): Promise<number> {
  const result = await window.api.invoke(
    "employerDb:clearAllPayslipAdditionsLineItems",
    dbPath,
    payslipId,
  );
  if (result.success) return result.updatedCount;
  throw new Error(result.error || "Failed to clear all additions line items");
}

export async function deleteAllPayslipAdditionsLineItems(
  dbPath: string,
  payslipId: string,
): Promise<number> {
  const result = await window.api.invoke(
    "employerDb:deleteAllPayslipAdditionsLineItems",
    dbPath,
    payslipId,
  );
  if (result.success) return result.deletedCount;
  throw new Error(result.error || "Failed to delete all additions line items");
}

// Deductions functions
export async function getDeductionsItemTypes(dbPath: string): Promise<any[]> {
  const result = await window.api.invoke(
    "employerDb:getDeductionsItemTypes",
    dbPath,
  );
  if (result.success) return result.itemTypes;
  throw new Error(result.error || "Failed to fetch deductions item types");
}

export async function upsertPayslipDeductionsLineItem(
  dbPath: string,
  item: any,
): Promise<any> {
  const result = await window.api.invoke(
    "employerDb:upsertPayslipDeductionsLineItem",
    dbPath,
    item,
  );
  if (result.success) return result.lineItem;
  throw new Error(result.error || "Failed to upsert deductions line item");
}

export async function deletePayslipDeductionsLineItem(
  dbPath: string,
  id: string,
): Promise<number> {
  const result = await window.api.invoke(
    "employerDb:deletePayslipDeductionsLineItem",
    dbPath,
    id,
  );
  if (result.success) return result.deletedCount;
  throw new Error(result.error || "Failed to delete deductions line item");
}

export async function clearAllPayslipDeductionsLineItems(
  dbPath: string,
  payslipId: string,
): Promise<number> {
  const result = await window.api.invoke(
    "employerDb:clearAllPayslipDeductionsLineItems",
    dbPath,
    payslipId,
  );
  if (result.success) return result.updatedCount;
  throw new Error(result.error || "Failed to clear all deductions line items");
}

export async function deleteAllPayslipDeductionsLineItems(
  dbPath: string,
  payslipId: string,
): Promise<number> {
  const result = await window.api.invoke(
    "employerDb:deleteAllPayslipDeductionsLineItems",
    dbPath,
    payslipId,
  );
  if (result.success) return result.deletedCount;
  throw new Error(result.error || "Failed to delete all deductions line items");
}
