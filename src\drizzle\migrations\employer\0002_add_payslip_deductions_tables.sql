CREATE TABLE IF NOT EXISTS `payslip_deductions_item_types` (
	`id` text PRIMARY KEY NOT NULL,
	`code` text NOT NULL,
	`display_label` text NOT NULL,
	`created_at` integer DEFAULT (strftime('%s','now')) NOT NULL,
	`updated_at` integer DEFAULT (strftime('%s','now')) NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX IF NOT EXISTS `uk_payslip_deductions_item_types_code` ON `payslip_deductions_item_types` (`code`);--> statement-breakpoint
CREATE TABLE IF NOT EXISTS `payslip_deductions_line_items` (
	`id` text PRIMARY KEY NOT NULL,
	`payslip_id` text NOT NULL,
	`item_type_id` text NOT NULL,
	`amount` real NOT NULL,
	`is_repeating` integer DEFAULT false NOT NULL,
	`zeroise_next` integer DEFAULT false NOT NULL,
	`created_at` integer DEFAULT (strftime('%s','now')) NOT NULL,
	`updated_at` integer DEFAULT (strftime('%s','now')) NOT NULL,
	FOREIG<PERSON> KEY (`payslip_id`) REFERENCES `payslips`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`item_type_id`) REFERENCES `payslip_deductions_item_types`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS `idx_deductions_line_items_payslip` ON `payslip_deductions_line_items` (`payslip_id`);
