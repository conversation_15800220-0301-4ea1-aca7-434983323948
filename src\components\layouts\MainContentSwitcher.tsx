"use client";
import React from "react";
import { useNavigationStore } from "@/store/navigation-store";
import PayrollManagement from "@/components/main-sections/payroll/page";
import RtiPage from "@/components/main-sections/rti/page";
import ReportsPage from "@/components/main-sections/reports/page";
import PensionsPage from "@/components/main-sections/pensions/page";
import EmployeesPage from "@/components/main-sections/employees/page";
import EmployerPage from "@/components/main-sections/employer/page";
import HmrcPaymentsPage from "@/components/main-sections/hmrc-payments/page";
import CisPage from "@/components/main-sections/cis/page";
import { Dashboard } from "@/components/main-sections/dashboard/page";

import { useEffect } from "react";
import { useEmployerDBContext } from "@/providers/employer-db-provider";

export const MainContentSwitcher: React.FC = () => {
  const { openEmployers } = useEmployerDBContext();
  const setActiveEmployer = useNavigationStore((s) => s.setActiveEmployer);
  const setGlobalSection = useNavigationStore((s) => s.setGlobalSection);

  useEffect(() => {
    // On mount, if there are no open employers, reset navigation state
    if (openEmployers && openEmployers.length === 0) {
      setActiveEmployer(null);
      setGlobalSection("dashboard");
    }
  }, [openEmployers, setActiveEmployer, setGlobalSection]);
  const activeEmployerId = useNavigationStore((s) => s.activeEmployerId);
  const globalSection = useNavigationStore((s) => s.globalSection);
  const employerSections = useNavigationStore((s) => s.employerSections);
  console.log("[MainContentSwitcher] RENDER", {
    activeEmployerId,
    globalSection,
    employerSections,
  });

  // Show dashboard if selected, regardless of employer state
  if (globalSection === "dashboard") {
    return <Dashboard />;
  }

  // If no employer is open, show dashboard as fallback
  if (!activeEmployerId) {
    return <Dashboard />;
  }

  // Section for the current employer
  const section = employerSections[activeEmployerId] || "payroll";
  console.log("[MainContentSwitcher] Rendering section:", section);

  switch (section) {
    case "payroll":
      return <PayrollManagement />;
    case "rti":
      return <RtiPage />;
    case "reports":
      return <ReportsPage />;
    case "pensions":
      return <PensionsPage />;
    case "employees":
      return <EmployeesPage />;
    case "employer":
      return <EmployerPage />;
    case "hmrc":
      return <HmrcPaymentsPage />;
    case "cis":
      return <CisPage />;
    default:
      return <PayrollManagement />;
  }
};

export default MainContentSwitcher;
