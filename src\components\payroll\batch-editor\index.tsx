"use client";

import { EMPLOYER_DB_LOCALSTORAGE_PREFIX } from "@/constants/file";

import React, { useMemo, useCallback, CSSProperties, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  FileText,
  Settings,
  ArrowUp,
  ArrowDown,
  MoveHorizontal,
  MoveVertical,
  GripHorizontal,
} from "lucide-react";
import ViewNavigation from "../view-navigation";
import {
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  SortingState,
  useReactTable,
  Header,
  Cell,
} from "@tanstack/react-table";

// DnD imports
import {
  DndContext,
  KeyboardSensor,
  MouseSensor,
  TouchSensor,
  closestCenter,
  type DragEndEvent,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { restrictToHorizontalAxis } from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  horizontalListSortingStrategy,
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

// Import types and utilities from refactored files
import { BatchEditorProps, availableColumns, CellChangeHandler } from "./types";
import { useTabNavigation } from "./navigation";
import { createDynamicColumns } from "./utils";
import { useEmployeesForPeriod } from "@/hooks/tanstack-query/useEmployeesForPeriod";

// Draggable header component
const DraggableTableHeader = ({ header }: { header: Header<any, unknown> }) => {
  const { attributes, isDragging, listeners, setNodeRef, transform } =
    useSortable({
      id: header.column.id,
    });

  const style: CSSProperties = {
    opacity: isDragging ? 0.8 : 1,
    position: "relative",
    transform: CSS.Translate.toString(transform), // translate instead of transform to avoid squishing
    transition: "width transform 0.2s ease-in-out",
    whiteSpace: "nowrap",
    zIndex: isDragging ? 1 : 0,
  };

  return (
    <th
      colSpan={header.colSpan}
      ref={setNodeRef}
      style={style}
      className={`px-3 py-3 text-center text-sm font-normal ${header.column.getCanSort() ? "cursor-pointer select-none" : ""}`}
    >
      <div
        className={`flex flex-col ${header.column.id === "name" ? "items-start" : "items-center"}`}
      >
        {/* Header content with sort indicator */}
        <div
          className={`flex items-center space-x-2 ${header.column.id === "status" ? "w-full justify-center" : header.column.id === "name" ? "justify-start" : "justify-center"}`}
        >
          {header.isPlaceholder
            ? null
            : flexRender(header.column.columnDef.header, header.getContext())}
          {header.column.getCanSort() && (
            <div className="ml-2">
              {header.column.getIsSorted() === "asc" ? (
                <ArrowUp className="h-4 w-4" />
              ) : (
                <ArrowDown className="h-4 w-4" />
              )}
            </div>
          )}
        </div>

        {/* Drag handle below the header content - or empty space for fixed columns */}
        {header.column.id !== "name" && header.column.id !== "status" ? (
          <button
            {...attributes}
            {...listeners}
            className="mt-1 cursor-move opacity-50 hover:opacity-100"
            onClick={(e) => e.stopPropagation()} // Prevent sorting when clicking the drag handle
          >
            <GripHorizontal className="h-3 w-3" />
          </button>
        ) : (
          <div className="mt-1 h-3"></div> /* Empty space to maintain alignment */
        )}
      </div>
    </th>
  );
};

// Cell component that moves with the header during dragging
const DragAlongCell = ({
  cell,
  isFirstRow = false,
}: {
  cell: Cell<any, unknown>;
  isFirstRow?: boolean;
}) => {
  const { isDragging, setNodeRef, transform } = useSortable({
    id: cell.column.id,
  });

  const style: CSSProperties = {
    opacity: isDragging ? 0.8 : 1,
    position: "relative",
    transform: CSS.Translate.toString(transform),
    transition: "width transform 0.2s ease-in-out",
    zIndex: isDragging ? 1 : 0,
    paddingTop: isFirstRow ? "12px" : undefined,
  };

  return (
    <td
      style={style}
      ref={setNodeRef}
      className="px-3 py-0.5 align-middle text-xs"
    >
      {flexRender(cell.column.columnDef.cell, cell.getContext())}
    </td>
  );
};

const BatchEditor: React.FC<BatchEditorProps> = ({
  periodId,
  activeView = "batch",
  onSwitchToOverview,
  onSwitchToBatch,
  onEmployeeSelect,
  initialStatusFilter = "all",
  onStatusFilterChange,
  initialSelectedColumns = [
    "salary",
    "hourlyRate",
    "hours",
    "bonus",
    "deduction",
  ],
  onSelectedColumnsChange,
  initialSorting = [{ id: "name", desc: false }],
  onSortingChange,
}) => {
  // State for selected columns
  const [selectedColumns, setSelectedColumns] = useState<string[]>(
    initialSelectedColumns,
  );

  // State for status filter
  const [statusFilter, setStatusFilter] = useState<"all" | "open" | "closed">(
    initialStatusFilter,
  );

  // Employee data hooked from DB
  const rawEmployees = useEmployeesForPeriod(periodId);
  // Map to BatchEmployee with default numeric values
  const mapToBatch = useCallback(
    (emp: { id: string; name: string; status: string }) => ({
      id: emp.id,
      name: emp.name,
      status: emp.status,
      salary: 0,
      hourlyRate: 0,
      hours: 0,
      bonus: 0,
      commission: 0,
      deduction: 0,
      pensionEe: 0,
      pensionEr: 0,
      onEmployeeSelect,
    }),
    [onEmployeeSelect],
  );
  const [employees, setEmployees] = useState(() =>
    rawEmployees.map(mapToBatch),
  );
  useEffect(() => {
    setEmployees(rawEmployees.map(mapToBatch));
  }, [rawEmployees, mapToBatch]);

  // State for sorting
  const [sorting, setSorting] = useState<SortingState>(initialSorting);

  // State for column order
  const [columnOrder, setColumnOrder] = useState<string[]>(() => {
    // Try to get the saved column order from localStorage
    const savedColumnOrder =
      typeof window !== "undefined"
        ? localStorage.getItem("batchEditorColumnOrder")
        : null;

    if (savedColumnOrder) {
      try {
        // Parse the saved column order
        const parsedOrder = JSON.parse(savedColumnOrder) as string[];

        // Ensure all selected columns are included (in case new columns were added)
        const missingColumns = ["name", "status", ...selectedColumns].filter(
          (col) => !parsedOrder.includes(col),
        );

        // Return the saved order with any missing columns appended
        return [...parsedOrder, ...missingColumns];
      } catch (error) {
        console.error("Error parsing saved column order:", error);
      }
    }

    // Default to base columns (name, status) followed by selected columns
    return ["name", "status", ...selectedColumns];
  });

  // State for tab navigation mode (true = column-wise, false = row-wise)
  const [columnWiseNavigation, setColumnWiseNavigation] = useState<boolean>(
    () => {
      // Try to get the saved preference from localStorage
      const savedPreference =
        typeof window !== "undefined"
          ? localStorage.getItem("batchEditorNavMode")
          : null;
      return savedPreference === "row" ? false : true; // Default to column-wise if not found
    },
  );

  // Save navigation preference when it changes
  const handleNavigationModeChange = useCallback((isColumnWise: boolean) => {
    // Get the currently focused element before changing modes
    const activeElement = document.activeElement as HTMLElement;
    const focusedInput =
      activeElement?.tagName === "INPUT" ? activeElement : null;

    // Update the navigation mode
    setColumnWiseNavigation(isColumnWise);

    // Save preference to localStorage
    if (typeof window !== "undefined") {
      localStorage.setItem(
        "batchEditorNavMode",
        isColumnWise ? "column" : "row",
      );
    }

    // Re-focus the same input if it was focused before
    if (focusedInput) {
      // Use a small timeout to ensure the DOM has updated
      setTimeout(() => {
        (focusedInput as HTMLInputElement).focus();
        (focusedInput as HTMLInputElement).select();
      }, 0);
    }
  }, []);

  // Save column order to localStorage when it changes
  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem(
        "batchEditorColumnOrder",
        JSON.stringify(columnOrder),
      );
    }
  }, [columnOrder]);

  // Handle column selection
  const handleColumnToggle = (columnId: string) => {
    let newColumns: string[];
    if (selectedColumns.includes(columnId)) {
      // Remove column from selected columns
      newColumns = selectedColumns.filter((id) => id !== columnId);
      // Also remove from column order
      setColumnOrder((prev) => prev.filter((id) => id !== columnId));
    } else {
      // Add column to selected columns
      newColumns = [...selectedColumns, columnId];
      // Also add to column order at the end
      setColumnOrder((prev) => [...prev, columnId]);
    }
    setSelectedColumns(newColumns);
    onSelectedColumnsChange?.(newColumns);
  };

  // Handle cell value changes - memoized to prevent re-renders
  const handleCellChange: CellChangeHandler = useCallback(
    (employeeId, columnId, value) => {
      const numericValue = value ?? 0;
      setEmployees((prev) =>
        prev.map((employee) => {
          if (employee.id === employeeId) {
            return {
              ...employee,
              [columnId]: numericValue,
            };
          }
          return employee;
        }),
      );
    },
    [],
  );

  // Create dynamic columns based on selected columns
  const columns = useMemo(
    () => createDynamicColumns(selectedColumns, handleCellChange),
    [selectedColumns, handleCellChange],
  );

  // Filter employees based on status filter
  const filteredEmployees = useMemo(() => {
    return employees.filter((emp) => {
      return statusFilter === "all" || emp.status === statusFilter;
    });
  }, [employees, statusFilter]);

  // Use the custom tab navigation hook
  const handleTabNavigation = useTabNavigation(columnWiseNavigation);

  // Use an effect to notify parent of sorting changes
  React.useEffect(() => {
    onSortingChange?.(sorting);
  }, [sorting, onSortingChange]);

  // Set up DnD sensors
  const sensors = useSensors(
    useSensor(MouseSensor, {}),
    useSensor(TouchSensor, {}),
    useSensor(KeyboardSensor, {}),
  );

  // Handle drag end event
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    if (active && over && active.id !== over.id) {
      setColumnOrder((columnOrder) => {
        const oldIndex = columnOrder.indexOf(active.id as string);
        const newIndex = columnOrder.indexOf(over.id as string);
        return arrayMove(columnOrder, oldIndex, newIndex);
      });
    }
  };

  // Initialize TanStack Table
  const table = useReactTable({
    data: filteredEmployees,
    columns,
    state: {
      sorting,
      columnOrder,
    },
    onSortingChange: setSorting,
    onColumnOrderChange: setColumnOrder,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    enableSorting: true,
    enableSortingRemoval: false,
    sortingFns: {
      name: (rowA, rowB, columnId) => {
        const valueA = rowA.getValue(columnId) as string;
        const valueB = rowB.getValue(columnId) as string;
        return valueA.localeCompare(valueB);
      },
    },
    enableMultiSort: false,
  });

  return (
    <DndContext
      collisionDetection={closestCenter}
      modifiers={[restrictToHorizontalAxis]}
      onDragEnd={handleDragEnd}
      sensors={sensors}
    >
      <div className="mb-1 w-full space-y-4 overflow-visible">
        <div className="flex items-center justify-between">
          <ViewNavigation
            activeView={activeView}
            onSwitchToOverview={onSwitchToOverview}
            onSwitchToBatch={onSwitchToBatch}
          />
          <div className="flex items-center gap-2">
            <h2 className="text-xl font-semibold">
              Batch Edit - {periodId.replace(/-/g, " ")}
            </h2>

            {/* Status filter */}
            <div className="bg-muted/20 ml-4 flex items-center gap-1 rounded-md border p-1">
              <Button
                size="sm"
                variant={statusFilter === "all" ? "default" : "ghost"}
                onClick={() => {
                  setStatusFilter("all");
                  onStatusFilterChange?.("all");
                }}
                className="h-7 text-xs"
              >
                All
              </Button>
              <Button
                size="sm"
                variant={statusFilter === "open" ? "default" : "ghost"}
                onClick={() => {
                  setStatusFilter("open");
                  onStatusFilterChange?.("open");
                }}
                className="h-7 text-xs"
              >
                Open
              </Button>
              <Button
                size="sm"
                variant={statusFilter === "closed" ? "default" : "ghost"}
                onClick={() => {
                  setStatusFilter("closed");
                  onStatusFilterChange?.("closed");
                }}
                className="h-7 text-xs"
              >
                Closed
              </Button>
            </div>
          </div>

          <div className="flex gap-2">
            <Button
              variant={columnWiseNavigation ? "default" : "default"}
              size="sm"
              onClick={() => handleNavigationModeChange(!columnWiseNavigation)}
              title={
                columnWiseNavigation
                  ? "Switch to tab by rows (down each column)"
                  : "Switch to tab by columns (across each row)"
              }
              className={
                !columnWiseNavigation
                  ? "bg-pink-500 hover:bg-pink-400"
                  : "bg-pink-500 hover:bg-pink-400"
              }
            >
              {columnWiseNavigation ? (
                <>
                  <MoveHorizontal className="mr-2 h-4 w-4" />
                  Tab by Columns
                </>
              ) : (
                <>
                  <MoveVertical className="mr-2 h-4 w-4" />
                  Tab by Rows
                </>
              )}
            </Button>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" size="sm">
                  <Settings className="mr-2 h-4 w-4" />
                  Columns
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-4" align="end">
                <div className="flex flex-col gap-3">
                  {availableColumns.map((column) => (
                    <div
                      key={column.id}
                      className="flex items-center space-x-2"
                    >
                      <Checkbox
                        id={`column-${column.id}`}
                        checked={selectedColumns.includes(column.id)}
                        onCheckedChange={() => handleColumnToggle(column.id)}
                      />
                      <Label htmlFor={`column-${column.id}`}>
                        {column.name}
                      </Label>
                    </div>
                  ))}
                </div>
              </PopoverContent>
            </Popover>
            <Button variant="outline" size="sm">
              <FileText className="mr-2 h-4 w-4" />
              Export
            </Button>
          </div>
        </div>

        {/* Batch Edit Table with TanStack Table */}

        <div
          className="w-auto overflow-x-auto"
          style={{
            height: `calc(100vh - ${localStorage.getItem(`${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_visible`) === "true" ? 350 : 210}px)`,
            maxWidth: "100%",
          }}
        >
          <table
            className="w-auto justify-self-center text-xs"
            onKeyDown={handleTabNavigation}
            style={{ tableLayout: "fixed" }}
          >
            <colgroup>
              <col className="w-[200px] max-w-[250px] min-w-[200px]" />
              {/* First column (employees)*/}
              <col className="w-[100px] max-w-[120px] min-w-[100px]" />
              {/* Second column (status)*/}
              {/* Dynamic columns based on selection below */}
              {selectedColumns.map((col) => (
                <col
                  key={col}
                  className="w-[120px] max-w-[150px] min-w-[120px]"
                />
              ))}
            </colgroup>
            <thead className="bg-background sticky top-0 z-10 border-b-2 border-slate-300">
              {table.getHeaderGroups().map((headerGroup) => (
                <tr key={headerGroup.id} className="py-1">
                  <SortableContext
                    items={columnOrder}
                    strategy={horizontalListSortingStrategy}
                  >
                    {headerGroup.headers.map((header) => (
                      <DraggableTableHeader key={header.id} header={header} />
                    ))}
                  </SortableContext>
                </tr>
              ))}
            </thead>

            <tbody className="pt-2">
              {table.getRowModel().rows.map((row, rowIndex) => (
                <tr
                  key={row.id}
                  className={`data-[state=selected]:bg-muted transition-colors duration-50 even:bg-slate-100 hover:bg-slate-200 dark:even:bg-black/30 dark:hover:bg-zinc-800`}
                  style={rowIndex === 0 ? { marginTop: "8px" } : {}}
                >
                  <SortableContext
                    items={columnOrder}
                    strategy={horizontalListSortingStrategy}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <DragAlongCell
                        key={cell.id}
                        cell={cell}
                        isFirstRow={rowIndex === 0}
                      />
                    ))}
                  </SortableContext>
                </tr>
              ))}
            </tbody>

            <tfoot className="bg-background sticky bottom-0 z-10 text-slate-700 shadow-slate-300 dark:text-slate-400">
              {table.getFooterGroups().map((footerGroup) => (
                <tr key={footerGroup.id}>
                  {footerGroup.headers.map((header, index) => {
                    return (
                      <td
                        key={header.id}
                        className={`px-3 py-4 align-middle text-sm ${
                          index <= 1 ? "border-t" : ""
                        }`}
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.footer,
                              header.getContext(),
                            )}
                      </td>
                    );
                  })}
                </tr>
              ))}
            </tfoot>
          </table>
        </div>
      </div>
    </DndContext>
  );
};

export default BatchEditor;
