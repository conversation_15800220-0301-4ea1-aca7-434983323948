"use client";

import React from "react";
import { ClickableCheckbox } from "@/components/ui/clickable-checkbox";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { RefreshCw } from "lucide-react";
import { usePayslipReadOnly } from "@/providers/payslip-readonly-provider";

interface RepeatingControlProps {
  id: string;
  isChecked: boolean;
  onChange: (checked: boolean) => void;
  className?: string;
  disabled?: boolean;
}

export const RepeatingControl: React.FC<RepeatingControlProps> = ({
  id,
  isChecked,
  onChange,
  className = "",
  disabled: externalDisabled,
}) => {
  // Get read-only state from context
  const { isReadOnly, getDisabledClassName } = usePayslipReadOnly();

  // Combine external disabled prop with read-only state
  const isDisabled = externalDisabled || isReadOnly;
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <div className={getDisabledClassName(className)}>
          <ClickableCheckbox
            id={`repeating-${id}`}
            className="ml-2 size-4 data-[state=checked]:border-emerald-600 data-[state=checked]:bg-emerald-400"
            checked={isChecked}
            onCheckedChange={(checked) => onChange(checked === true)}
            disabled={isDisabled}
            label={
              <RefreshCw
                className={`size-4 ${
                  isChecked && !isDisabled
                    ? "text-emerald-500 dark:text-emerald-300"
                    : "text-muted-foreground"
                }`}
              />
            }
            wrapperClassName="flex items-center mr-2"
          />
        </div>
      </TooltipTrigger>
      <TooltipContent>
        <p>Repeating</p>
      </TooltipContent>
    </Tooltip>
  );
};
