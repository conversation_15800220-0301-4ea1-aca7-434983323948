"use client";

import React from "react";
import { ClickableCheckbox } from "@/components/ui/clickable-checkbox";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Eye } from "lucide-react";
import { usePayslipReadOnly } from "@/providers/payslip-readonly-provider";

interface ShowOnPayslipControlProps {
  id: string;
  isChecked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
  className?: string;
}

export const ShowOnPayslipControl: React.FC<ShowOnPayslipControlProps> = ({
  id,
  isChecked,
  onChange,
  disabled = false,
  className = "",
}) => {
  // Get read-only state from context
  const { isReadOnly, getDisabledClassName } = usePayslipReadOnly();

  // Combine external disabled prop with read-only state
  const isDisabled = disabled || isReadOnly;
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <div className={getDisabledClassName(className)}>
          <ClickableCheckbox
            id={`show-on-payslip-${id}`}
            className="h-4 w-4 data-[state=checked]:border-blue-600 data-[state=checked]:bg-blue-400"
            checked={isChecked}
            onCheckedChange={(checked) => onChange(checked === true)}
            disabled={isDisabled}
            label={
              <Eye
                className={`h-5 w-5 ${
                  isChecked && !isDisabled
                    ? "text-blue-500 dark:text-blue-300"
                    : "text-muted-foreground"
                }`}
              />
            }
            wrapperClassName="flex items-center"
          />
        </div>
      </TooltipTrigger>
      <TooltipContent>
        <p>Show on Payslip</p>
      </TooltipContent>
    </Tooltip>
  );
};
